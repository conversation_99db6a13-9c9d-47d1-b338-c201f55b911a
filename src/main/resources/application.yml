# Server Configuration
server:
  port: 8081
  servlet:
    context-path: /api/withdrawal
  ssl:
    enabled: false
  shutdown: graceful

# Microservices Properties
spring:
  profiles:
    active: local
  application:
    name: withdrawal
  threads:
    virtual:
      enabled: true
    # Database Properties (Azure SQL Server)
  datasource:
    driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    url: ****************************************************************************************************************************************
    username: atmi_qa
    password: Y7mZ!p4Qh-2Vw_T9
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
    properties:
      hibernate:
        format_sql: true
        use_sql_comments: true
    open-in-view: true

# API Documentation
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    enabled: true

logging.level:
  root: INFO

special-card-no-list: ****************,****************,****************

rest:
  eapi:
    coop-service:
      base-url: https://atmi-qa.se.scb.co.th/api/mock
      endpoint:
        coop-transaction: /v4/coop/transaction
    payment-domain:
      base-url: https://atmi-qa.se.scb.co.th/api/mock
      endpoint:
        debit-credit: /v4/accounts/deposits/debitCreditService
        debit-credit-verify: /v1/legacy/deposit/debitcreditverify
        balance-inquiry: /v1/accounts/deposits/balanceInquiry
        cardless-verify: /v1/gateway/cardless/verify
        cardless-withdrawal: /v1/gateway/cardless/withdrawal
        cardless-reversal: /v1/gateway/cardless/reversal
    notification:
      base-url: https://atmi-qa.se.scb.co.th/api/mock
      endpoint:
        send-message: /v4/accounts/deposits/debitCreditService
    loan-info-service:
      base-url: https://atmi-qa.se.scb.co.th/api/mock
      endpoint:
        inquiry: /v1/accounts/loans/extendedInfo/inquiry

# Kafka Configuration
kafka:
  enabled: true
  servers: cdckks1.se.scb.co.th:9092,cdckks2.se.scb.co.th:9092,cdckks3.se.scb.co.th:9092
  topic:
    transfer-events: SCB_EVENT_ACCOUNTS_DEBITCARDS_TRANSACTIONS

# Redis Configuration
redis:
  host: scbbchqsearedis001dev.redis.cache.windows.net
  port: 6380
