package com.scb.atmi.constant;

public class CommonConstants {
    public static final String TRANSACTION_TYPE_ALLOWED = "10";
    public static final String FIXE_ACCOUNT_NOT_ALLOWED = "13";

    public static final String TRANSACTION_STATUS_WITHDRAWAL_IN_PROGRESS = "WIT<PERSON>RAWAL_IN_PROGRESS";
    public static final String TRANSACTION_STATUS_WITHDRAWAL_SUCCESS = "WITHDRAWAL_SUCCESS";
    public static final String TRANSACTION_STATUS_WITHDRAWAL_FAILED = "WITHDRAWAL_ERROR";

    public static final String TRANSACTION_STATUS_WITHDRAWAL_REVERSE_IN_PROGRESS = "WITHDRAWAL_REVERSE_IN_PROGRESS";
    public static final String TRANSACTION_STATUS_WITHDRAWAL_REVERSE_SUCCESS = "WITHDRAWAL_REVERSE_SUCCESS";
    public static final String TRANSACTION_STATUS_WITHDRAWA_REVERSEL_FAILED = "WITHDRAWAL_REVERSE_ERROR";

    public static final String PYMD_TRANSACTION_REVDEBIT_TYPE = "REVDEBIT";
    public static final String PYMD_TRANSACTION_DEBIT_TYPE = "DEBIT";

    public static final String PYMD_ACCOUNT_BALANCE_TYPE_AVAILBALANCE = "AVAILBALANCE";
    public static final String PYMD_ACCOUNT_BALANCE_TYPE_BALANCE = "BALANCE";

    public static final String COOP_MSG_TYPE_RQST = "RQST";
    public static final String COOP_MSG_TYPE_RVSL = "RVSL";

    public static final String COOP_STATUS_CODE_SUCCESS = "0000";
    public static final String COOP_RESPONSE_CODE_SUCCESS = "000";

}
