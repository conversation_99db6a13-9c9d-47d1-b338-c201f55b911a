package com.scb.atmi.constant;

public final class AtmResponseCodes {

    private AtmResponseCodes() {
    }

    public static final String APPROVED = "00";
    public static final String DATABASE_ERROR = "54";
    public static final String INVALID_ACCOUNT = "56";
    public static final String TRANSACTION_NOT_ALLOWED = "71";
    public static final String DESTINATION_NOT_AVAILABLE = "72";
    public static final String MISSING_INVALID_REQUEST = "74";

    public static final String MSG_DUPLICATE_TRANSACTION = "Duplicate transaction";

    public static String getDescription(String responseCode) {
        return switch (responseCode) {
            case APPROVED -> "Success";
            case DATABASE_ERROR -> "Database error";
            case INVALID_ACCOUNT -> "Invalid transaction";
            case TRANSACTION_NOT_ALLOWED -> "Transaction not allow";
            case DESTINATION_NOT_AVAILABLE -> "Destination not available";
            case MISSING_INVALID_REQUEST -> "Missing/Invalid Request";
            default -> "UNKNOWN ERROR";
        };
    }
}
