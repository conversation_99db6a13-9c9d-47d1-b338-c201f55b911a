package com.scb.atmi.constant;

/**
 * ATM Status Flags Constants
 * Migrated from COBOL AAUL.txt status flag definitions
 * 
 * Original COBOL: AAUL.txt - STATUS FLAG section
 * 
 * These status flags are used in ATM transaction logging to indicate
 * specific transaction conditions and outcomes.
 */
public final class AtmStatusFlags {

    private AtmStatusFlags() {
        // Utility class - prevent instantiation
    }

    /**
     * Transaction not allowed or account unassigned
     * COBOL: '6' - 'TXN-NOT-ALLOW' / 'ACCT UNASSIGNED'
     */
    public static final String TRANSACTION_NOT_ALLOWED = "6";

    /**
     * Account is held
     * COBOL: '8' - 'ACCOUNT IS HELD'
     */
    public static final String ACCOUNT_HELD = "8";

    /**
     * System error condition
     * COBOL: 'S' - 'SYSTEM-ERROR'
     */
    public static final String SYSTEM_ERROR = "S";

    /**
     * Insufficient fund for SAF or online transaction
     * COBOL: '5' - 'INSUFF-FUND-SAF' / 'INSUFF-FUND-ONL'
     */
    public static final String INSUFFICIENT_FUND_SAF = "5";
}
