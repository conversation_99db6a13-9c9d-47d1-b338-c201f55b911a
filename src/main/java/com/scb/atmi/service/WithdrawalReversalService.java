package com.scb.atmi.service;

import com.scb.atmi.constant.AtmResponseCodes;
import com.scb.atmi.enums.ResponseFrom;
import com.scb.atmi.enums.RevesalType;
import com.scb.atmi.library.communication.rest.RestServiceException;
import com.scb.atmi.library.dto.DuplicateCheckDto;
import com.scb.atmi.library.entity.TransactionLogEntity;
import com.scb.atmi.library.log.AppLogger;
import com.scb.atmi.library.log.LoggerFactory;
import com.scb.atmi.library.model.CardRefCheckResult;
import com.scb.atmi.library.model.DuplicateCheckResult;
import com.scb.atmi.library.model.constant.HeaderConstant;
import com.scb.atmi.library.model.constant.ResponseConstants;
import com.scb.atmi.library.model.request.coop.CoopRequest;
import com.scb.atmi.library.model.request.notification.NotiRequest;
import com.scb.atmi.library.model.request.notification.PublicationBody;
import com.scb.atmi.library.model.request.paymentDomain.DebitCreditRequest;
import com.scb.atmi.library.model.request.paymentDomain.debitCredit.AccountRequest;
import com.scb.atmi.library.model.request.paymentDomain.debitCredit.Fees;
import com.scb.atmi.library.model.request.paymentDomain.debitCredit.RemittanceInfo;
import com.scb.atmi.library.model.request.paymentDomain.debitCredit.StatementDescription;
import com.scb.atmi.library.model.response.coop.COOPMsg;
import com.scb.atmi.library.model.response.coop.CoopResponse;
import com.scb.atmi.library.model.response.paymentDomain.DebitCreditResponse;
import com.scb.atmi.library.repository.TransactionLogRepository;
import com.scb.atmi.library.service.CardProfileService;
import com.scb.atmi.library.service.CoopService;
import com.scb.atmi.library.service.DuplicateCheckService;
import com.scb.atmi.library.service.PaymentDomainService;
import com.scb.atmi.library.service.async.AsyncNotificationService;
import com.scb.atmi.library.service.cache.ResponseLookupService;
import com.scb.atmi.library.util.AtmUtil;
import com.scb.atmi.library.util.CardUtils;
import com.scb.atmi.model.request.WithdrawalReversalRequest;
import com.scb.atmi.model.response.WithdrawalReversalResponse;
import com.scb.atmi.model.response.WithdrawalTransactionResponse;
import com.scb.atmi.util.COOPMsgGenerator;
import com.scb.atmi.util.CommonUtil;
import com.scb.atmi.util.DuplicateCheckMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.UUID;

import static com.scb.atmi.constant.CommonConstants.*;

@Service
@RequiredArgsConstructor
public class WithdrawalReversalService {
    private final AppLogger log = LoggerFactory.getAppLogger(this.getClass());
    private final DuplicateCheckService duplicateCheckService;
    private final CardProfileService cardProfileService;
    private final TransactionLogRepository transactionLogRepository;
    private final CoopService coopService;
    private final AsyncNotificationService asyncNotificationService;
    private final PaymentDomainService paymentDomainService;
    private final ResponseLookupService responseLookupService;

    public WithdrawalReversalResponse processWithdrawalReversal(HttpHeaders headers, WithdrawalReversalRequest request) {
        log.info("[process-withdrawal-reversal] start process");
        String correlationId = headers.getFirst(HeaderConstant.CORRELATION_ID);

        // Validate transaction request
        log.info("[process-withdrawal-reversal] validating transaction request");
        WithdrawalReversalResponse validationResponse = validateReversalTransaction(request);
        if (validationResponse != null) {
            log.info("[process-withdrawal-reversal] validation failed, returning error response");
            return validationResponse;
        }

        // Check card reference
        log.info("[process-withdrawal-reversal] checking card reference");
        CardRefCheckResult cardRefResult = cardProfileService.checkCardRef(request.getCardReference(), request.getCardNumber());
        if (cardRefResult.getStatus() != CardRefCheckResult.Status.SUCCESS) {
            log.info("[process-withdrawal-reversal] card reference check failed: {}", cardRefResult.getMessage());
            if (cardRefResult.getStatus() == CardRefCheckResult.Status.ERROR_71) {
                return WithdrawalReversalResponse.createErrorResponse(
                    AtmResponseCodes.INVALID_ACCOUNT, AtmResponseCodes.getDescription(AtmResponseCodes.INVALID_ACCOUNT));
            }
            return WithdrawalReversalResponse.createErrorResponse(
                AtmResponseCodes.DATABASE_ERROR, AtmResponseCodes.getDescription(AtmResponseCodes.DATABASE_ERROR));
        } else {
            log.info("[process-withdrawal-reversal] card reference check passed");
            request.setCardReference(cardRefResult.getCardRef());
        }

        // Check for duplicate transactions
        log.info("[process-withdrawal-reversal] checking duplicate reversal transaction");
        DuplicateCheckDto duplicateCheckDto = DuplicateCheckMapper.fromWithdrawalReversalRequest(request, false);
        DuplicateCheckResult duplicateCheck =
                duplicateCheckService.checkDuplicateReversalTransaction(duplicateCheckDto);

        if (ResponseConstants.MESSAGE_TYPE_0400.equals(request.getMessageType())) {
            log.info("[process-withdrawal-reversal] checking duplicate reversal transaction for 0400");
            if (duplicateCheck.isDuplicate()) {
                log.info("[process-withdrawal-reversal] duplicate reversal transaction detected");
                return handleDuplicateTransactionForReversal(); // 56
            }

            if (duplicateCheck.isError()) {
                log.info("[process-withdrawal-reversal] duplicate reversal check error occurred");
                return handleDuplicateCheckErrorForReversal(); // 54
            }
        } else if (
                ResponseConstants.MESSAGE_TYPE_9220.equals(request.getMessageType()) ||
                        ResponseConstants.MESSAGE_TYPE_9221.equals(request.getMessageType())) {
            log.info("[process-withdrawal-reversal] checking duplicate reversal transaction for 9220/9221");
            if (duplicateCheck.isReturnSuccess()) {
                log.info("[process-withdrawal-reversal] duplicate reversal check returned success, returning success response");
                // Stop and return success response
                return WithdrawalReversalResponse.createSuccessResponse(BigDecimal.ZERO, BigDecimal.ZERO);
            }

            if (duplicateCheck.isDuplicate()) {
                log.info("[process-withdrawal-reversal] duplicate reversal transaction detected");
                return handleDuplicateTransactionForReversal(); // 56
            }

            if (duplicateCheck.isError()) {
                log.info("[process-withdrawal-reversal] duplicate reversal check error occurred");
                return handleDuplicateCheckErrorForReversal();
            }
        } else {
            log.info("[process-withdrawal-reversal] unsupported message type: {}", request.getMessageType());
            return  handle71Error();
        }

        // Check original transaction
        log.info("[process-withdrawal-reversal] checking original transaction for reversal");
        DuplicateCheckDto duplicateOriginalCheckDto = DuplicateCheckMapper.fromWithdrawalReversalRequest(request, true);
        duplicateOriginalCheckDto.setMessageType("0200");
        DuplicateCheckResult duplicateOriginalCheck =
                duplicateCheckService.checkTransactionError(duplicateOriginalCheckDto, TRANSACTION_STATUS_WITHDRAWAL_FAILED);

        if (!duplicateOriginalCheck.isDuplicate()) {
            log.info("[process-withdrawal-reversal] original transaction not found");
            return handle71Error();
        }

        if (duplicateOriginalCheck.isError()) {
            log.info("[process-withdrawal-reversal] original transaction duplicate check error");
            return handleDuplicateCheckErrorForReversal();
        }

        log.info("[process-withdrawal-reversal] inserting reversal transaction log");
        TransactionLogEntity transactionLog = insertReversalTransactionLog(request, TRANSACTION_STATUS_WITHDRAWAL_REVERSE_IN_PROGRESS);

        RevesalType reversalType = null;
        // Validate amount
        log.info("[process-withdrawal-reversal] validating amounts for reversal");
        if (request.getAmounts().getDispensedAmount() == null || request.getAmounts().getDispensedAmount().compareTo(BigDecimal.ZERO) == 0) {
            // Continue with reversal processing
            log.info("[process-withdrawal-reversal] continue with reversal processing (Full reversed)");
            reversalType = RevesalType.FULL;
        } else if (request.getAmounts().getDispensedAmount().compareTo(request.getAmounts().getTotalAmount()) > 0) {
            // Continue with reversal processing
            log.info("[process-withdrawal-reversal] continue with reversal processing (Over dispensed)");
            reversalType = RevesalType.OVER;
        } else if (request.getAmounts().getDispensedAmount().compareTo(request.getAmounts().getTotalAmount()) < 0) {
            // Continue with reversal processing
            log.info("[process-withdrawal-reversal] continue with reversal processing (Patial reversed)");
            reversalType = RevesalType.PARTIAL;
        } else if (request.getAmounts().getDispensedAmount().equals(request.getAmounts().getTotalAmount())) {
            // Return to success
            log.info("[process-withdrawal-reversal] return success to channel");
            return WithdrawalReversalResponse.createSuccessResponse(null, null);
        }

        // Withdraw process
        WithdrawalReversalResponse response = null;
        try {
            log.info("[process-withdrawal-reversal] starting reversal processing: {}", reversalType);
            return response = processWithdrawalByCardType(request, correlationId, reversalType);
        } catch (Exception e) {
            log.error("[process-withdrawal-reversal] error during reverse: {}", e.getMessage(), e);
            return WithdrawalReversalResponse.createErrorResponse(
                AtmResponseCodes.TRANSACTION_NOT_ALLOWED,
                AtmResponseCodes.getDescription(AtmResponseCodes.TRANSACTION_NOT_ALLOWED)
            );
        } finally {
            log.info("[process-withdrawal-reversal] updating transaction log");
            if (response != null && AtmResponseCodes.APPROVED.equals(response.getResponseCode())) {
                log.info("[process-withdrawal-reversal] reversal successful, updating status to success");
                transactionLog.setTransactionStatus(TRANSACTION_STATUS_WITHDRAWAL_REVERSE_SUCCESS);
            } else {
                log.info("[process-withdrawal-reversal] reversal failed, updating status to failed");
                if (response != null && AtmResponseCodes.DESTINATION_NOT_AVAILABLE.equals(response.getResponseCode())) {
                    transactionLog.setTimeoutFlag(true);
                }
                transactionLog.setTransactionStatus(TRANSACTION_STATUS_WITHDRAWA_REVERSEL_FAILED);
            }
            updateTransactionLog(transactionLog);
            log.info("[process-withdrawal-reversal] end process");
        }
    }

    public WithdrawalReversalResponse validateReversalTransaction(WithdrawalReversalRequest request) {
        String transactionType = request.getProcessingCode().getTransactionType();

        if (!TRANSACTION_TYPE_ALLOWED.equals(transactionType)) {
            return WithdrawalReversalResponse.createErrorResponse(
                    AtmResponseCodes.MISSING_INVALID_REQUEST, AtmResponseCodes.getDescription(AtmResponseCodes.MISSING_INVALID_REQUEST));
        }

        if (request.getAmounts().getTotalAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return WithdrawalReversalResponse.createErrorResponse(
                    AtmResponseCodes.MISSING_INVALID_REQUEST, AtmResponseCodes.getDescription(AtmResponseCodes.MISSING_INVALID_REQUEST));
        }
        return null;
    }

    @Transactional
    public TransactionLogEntity insertReversalTransactionLog(WithdrawalReversalRequest request, String status) {
        DuplicateCheckDto duplicateCheckDto = DuplicateCheckMapper.fromWithdrawalReversalRequest(request, true);
        String transactionLogKey = duplicateCheckService.generateTransactionLogKey(duplicateCheckDto);
        log.info("Inserting transaction log: {}", request);
        TransactionLogEntity transactionLog = TransactionLogEntity.builder()
                .atmTransactionId(transactionLogKey)
                .bankTerminalOwner(request.getBankTerminalOwner())
                .terminalId(request.getTerminalId())
                .sequenceNo(request.getTerminalSequence())
                .cardType(request.getCardType())
                .cardRef(request.getTerminalSequence())
                .cardMaskNo(request.getCardNumber())
                .fromAccount(request.getFromAccountNumber())
                .toAccount(request.getToAccountNumber())
                .messageType(request.getMessageType())
                .originalMessageType(request.getOriginalMessageType())
                .transactionType(request.getProcessingCode().getTransactionType())
                .transactionStatus(status)
                .transactionDate(request.getTransactionDate())
                .transactionTime(request.getTransactionTime())
                .postingDate(request.getPostingDate())
                .processingCode(getProcessingCode(request))
                .transactionAmount(request.getAmounts().getTotalAmount())
                .transactionFee(request.getAmounts().getFeeAmount())
                .currency(request.getCurrencyCode())
                .createdDate(LocalDateTime.now())
                .updatedDate(LocalDateTime.now())
                .build();
        return transactionLogRepository.save(transactionLog);
    }

    private String getProcessingCode(WithdrawalReversalRequest request) {
        log.info("[process-withdrawal-reversal] generating processing code for request: {}", request);
        String processingCode = request.getProcessingCode().getTransactionType() + request.getProcessingCode().getFromAccountType() + request.getProcessingCode().getToAccountType();
        log.info("[process-withdrawal-reversal] generated processing code: {}", processingCode);
        return processingCode;
    }

    public void updateTransactionLog(TransactionLogEntity transactionLog) {
        log.info("Update transaction log: {}", transactionLog);

        transactionLogRepository.save(transactionLog);
    }

    private WithdrawalReversalResponse processCoopWithdrawalReversal(WithdrawalReversalRequest request, String correlationId) {
        log.info("[withdrawal-reversal-coop] starting coop withdrawal reversal processing for request: {}", request);
        try {
            log.info("[withdrawal-reversal-coop] calling coop reverse service");
            CoopRequest coopRequest = buildCoopRequest(request);
            log.info("[withdrawal-reversal-coop] built coop request: {}", coopRequest);
            CoopResponse coopResponse = coopService.callReverseService(coopRequest, correlationId);
            COOPMsg coopMsg = coopResponse.getCOOPMsg();
            String statusCode = coopResponse.getDocRs().getMerchantCOOPRs().getStatus().getStatusCode();
            log.info("[withdrawal-reversal-coop] received coop response: {}", coopResponse);
            if (COOP_STATUS_CODE_SUCCESS.equals(statusCode) && COOP_RESPONSE_CODE_SUCCESS.equals(coopMsg.getResponseCode())){
                log.info("[withdrawal-reversal-coop] coop reverse successful, sending notification");
                NotiRequest notiRequest = buildNotiRequest(request, coopResponse, null);
                asyncNotificationService.sendNotificationAsync(notiRequest, correlationId);
            } else if (COOP_STATUS_CODE_SUCCESS.equals(statusCode) && !COOP_RESPONSE_CODE_SUCCESS.equals(coopMsg.getResponseCode())) {
                return WithdrawalReversalResponse.createErrorResponse(
                    coopMsg.getResponseCode(),
                    coopMsg.getResponseDesc()
                );
            } else {
                log.info("[withdrawal-reversal-coop] coop reverse failed with code: {}", coopResponse.getDocRs().getMerchantCOOPRs().getStatus().getStatusCode());
                return WithdrawalReversalResponse.createErrorResponse(
                    AtmResponseCodes.TRANSACTION_NOT_ALLOWED,
                    AtmResponseCodes.getDescription(AtmResponseCodes.TRANSACTION_NOT_ALLOWED)
                );
            }
            log.info("[withdrawal-reversal-coop] returning success response");
            return WithdrawalReversalResponse.createSuccessResponseCaseCoop(BigDecimal.ZERO, BigDecimal.ZERO);
        } catch (RestServiceException e) {
            log.error("[withdrawal-reversal-coop] timeout error occurred: {}", e);
            return WithdrawalReversalResponse.createErrorResponse(
                AtmResponseCodes.DESTINATION_NOT_AVAILABLE,
                AtmResponseCodes.getDescription(AtmResponseCodes.DESTINATION_NOT_AVAILABLE)
            );
        }catch (Exception e) {
            log.error("[withdrawal-reversal-coop] error during coop reverse: {}", e.getMessage(), e);
            return WithdrawalReversalResponse.createErrorResponse(
                AtmResponseCodes.TRANSACTION_NOT_ALLOWED,
                AtmResponseCodes.getDescription(AtmResponseCodes.TRANSACTION_NOT_ALLOWED)
            );
        }
    }

    private CoopRequest buildCoopRequest(WithdrawalReversalRequest request){
        log.info("[withdrawal-reversal-coop] building coop request for withdrawal reversal: {}", request);
        String uuid = UUID.randomUUID().toString().replace("-", "");
        log.info("[withdrawal-reversal-coop] generated UUID: {}", uuid);
        String coopMsg = COOPMsgGenerator.generateCOOPMsgForWithdrawalReversal(request);
        log.info("[withdrawal-reversal-coop] generated coop message: {}", coopMsg);
        CoopRequest coopRequest = CoopRequest.builder().docRq(CoopRequest.DocRq.builder()
                        .merchantCOOPRq(CoopRequest.DocRq.MerchantCOOPRq.builder()
                                .rqUID(uuid)
                                .tranType("KUSD")
                                .coopMsg(coopMsg)
                                .build())
                        .build())
                .build();
        log.info("[withdrawal-reversal-coop] built coop request: {}", coopRequest);
        return coopRequest;
    }

    private boolean isCoopCard(WithdrawalReversalRequest request) {
        log.info("[process-withdrawal-reversal] checking if card is coop card for request: {}", request);
        boolean isCoopCard = "18".equals(request.getCardType()) && "01".equals(request.getProcessingCode().getFromAccountType());
        log.info("[process-withdrawal-reversal] card type: {}, from account type: {}, is coop card: {}",
                request.getCardType(), request.getProcessingCode().getFromAccountType(), isCoopCard);
        return isCoopCard;
    }

    private WithdrawalReversalResponse processWithdrawalByCardType(WithdrawalReversalRequest request,
                                                                   String correlationId,
                                                                   RevesalType reversalType) {
        log.info("[process-withdrawal-reversal] processing withdrawal by card type for request: {}, reversal type: {}", request, reversalType);
        WithdrawalReversalResponse response = null;
        if (isCoopCard(request)) {
            log.info("[process-withdrawal-reversal] processing coop card withdrawal");
            response = processCoopWithdrawalReversal(request, correlationId);
        } else {
            // call post v4 debit credit
            log.info("[process-withdrawal-reversal] processing pymd reversal");
            switch (reversalType) {
                case FULL:
                    log.info("[process-withdrawal-reversal] reversal type is FULL");
                    response = processReversalPymd(request, correlationId, request.getAmounts().getTotalAmount());
                    break;
                case OVER:
                    log.info("[process-withdrawal-reversal] reversal type is OVER");
                    response = processReversalPymd(request, correlationId, request.getAmounts().getDispensedAmount());
                    break;
                case PARTIAL:
                    log.info("[process-withdrawal-reversal] reversal type is PARTIAL");
                    response = processReversalPymd(request, correlationId, request.getAmounts().getTotalAmount()); // REVDEBIT
                    response = processReversalPymd(request, correlationId, request.getAmounts().getDispensedAmount()); // DEBIT
                    break;
                default:
                    log.info("[process-withdrawal-reversal] no specific reversal type, using total amount");
                    response = WithdrawalReversalResponse.createSuccessResponse(null, null);
            }
        }
        log.info("[process-withdrawal-reversal] completed processing, response: {}", response);
        return response;
    }

    private WithdrawalReversalResponse processReversalPymd(WithdrawalReversalRequest request, String correlationId, BigDecimal amount) {
        log.info("[process-withdrawal-reversal] starting pymd reversal processing for request: {}, amount: {}", request, amount);
        try {
            log.info("[process-withdrawal-reversal] calling payment domain reverse service for amount: {}", amount);
            DebitCreditRequest debitCreditRequest = buildDebitCreditRequest(request, "", amount, PYMD_TRANSACTION_REVDEBIT_TYPE);
            log.info("[process-withdrawal-reversal] built debit credit request: {}", debitCreditRequest);
            DebitCreditResponse pymdResponse = paymentDomainService.postDebitCreditServiceInfo(debitCreditRequest, getOriginalSystem(request), correlationId);
            log.info("[process-withdrawal-reversal] received pymd response: {}", pymdResponse);

            BigDecimal availableBalance = null;
            BigDecimal ledgerBalance = null;
            if (checkResponseIsNull(pymdResponse)) {
                log.info("[process-withdrawal-reversal] extracting balance information from payment domain response");
                availableBalance = getBalance(pymdResponse, PYMD_ACCOUNT_BALANCE_TYPE_AVAILBALANCE);
                ledgerBalance = getBalance(pymdResponse, PYMD_ACCOUNT_BALANCE_TYPE_BALANCE);
                log.info("[process-withdrawal-reversal] extracted balances - available: {}, ledger: {}", availableBalance, ledgerBalance);
            }
            log.info("[process-withdrawal-reversal] payment domain reverse successful, sending notification");
            NotiRequest notiRequest = buildNotiRequest(request, null, pymdResponse);
            asyncNotificationService.sendNotificationAsync(notiRequest, correlationId);
            log.info("[process-withdrawal-reversal] returning success response");
            return WithdrawalReversalResponse.createSuccessResponseCasePymd(availableBalance, ledgerBalance);
        } catch (RestServiceException e) {
            log.error("[process-withdrawal-reversal] error occurred: {}", e.getMessage());
            String errorCode = AtmUtil.handleClientError(e);
            if ("JMS01".equals(errorCode) || AtmResponseCodes.DESTINATION_NOT_AVAILABLE.equals(errorCode)) {
                return WithdrawalReversalResponse.createErrorResponse(
                    AtmResponseCodes.DESTINATION_NOT_AVAILABLE,
                    AtmResponseCodes.getDescription(AtmResponseCodes.DESTINATION_NOT_AVAILABLE)
                );
            }
            String responseCode = responseLookupService.getResponseCode(
                request.getProcessingCode().getTransactionType(),
                ResponseFrom.PYMD.getValue(),
                errorCode
            );
            return WithdrawalReversalResponse.createErrorResponse(
                responseCode,
                AtmResponseCodes.getDescription(responseCode)
            );
        } catch (Exception e) {
            log.error("[process-withdrawal-reversal] unexpected error occurred: {}", e.getMessage(), e);
            return WithdrawalReversalResponse.createErrorResponse(
                AtmResponseCodes.TRANSACTION_NOT_ALLOWED,
                AtmResponseCodes.getDescription(AtmResponseCodes.TRANSACTION_NOT_ALLOWED)
            );
        }
    }

    private boolean checkResponseIsNull(DebitCreditResponse pymdResponse) {
        log.info("[process-withdrawal-reversal] checking if pymd response has valid balance info: {}", pymdResponse);
        boolean hasValidBalanceInfo = pymdResponse.getFromAccount() != null &&
            pymdResponse.getFromAccount().getBalanceInfo() != null &&
            pymdResponse.getFromAccount().getBalanceInfo().getAccountBalances() != null;
        log.info("[process-withdrawal-reversal] has valid balance info: {}", hasValidBalanceInfo);
        return hasValidBalanceInfo;
    }

    private NotiRequest buildNotiRequest(WithdrawalReversalRequest request,
                                         CoopResponse coopResponse,
                                         DebitCreditResponse debitCreditResponse) {
        log.info("[process-withdrawal-reversal] building notification request for withdrawal reversal: {}", request);
        String transactionLogKey = duplicateCheckService.generateTransactionLogKey(
                DuplicateCheckMapper.fromWithdrawalReversalRequest(request, request.getOriginalMessageType() != null)
        );
        log.info("[process-withdrawal-reversal] generated transaction log key: {}", transactionLogKey);
        String  subEventCode = "02";
        String messageType = "RVSL";
        NotiRequest notiRequest = NotiRequest.createNotiRequest(subEventCode,
                transactionLogKey,
                PublicationBody.builder()
                        .messageType(messageType)
                        .cardMaskedNumber(CardUtils.maskCardNumber(request.getCardNumber())) // Mask card number
                        .transactionAmount(request.getAmounts().getTotalAmount())
                        .transactionFee(request.getAmounts().getFeeAmount())
                        .currencyCode("764")
                        .fromAccount(request.getFromAccountNumber())
                        .toAccount(request.getToAccountNumber())
                        .build());
        log.info("[process-withdrawal-reversal] created base notification request: {}", notiRequest);
        if (coopResponse != null) {
            log.info("[process-withdrawal-reversal] processing coop response: {}", coopResponse);
            // TODO: set coop response to noti request
        } else if (debitCreditResponse != null) {
            log.info("[process-withdrawal-reversal] processing debit credit response: {}", debitCreditResponse);
            BigDecimal availableBalance = null;
            BigDecimal ledgerBalance = null;
            if (checkResponseIsNull(debitCreditResponse)) {
                availableBalance = getBalance(debitCreditResponse, PYMD_ACCOUNT_BALANCE_TYPE_AVAILBALANCE);
                ledgerBalance = getBalance(debitCreditResponse, PYMD_ACCOUNT_BALANCE_TYPE_BALANCE);
                log.info("[process-withdrawal-reversal] extracted balances - available: {}, ledger: {}", availableBalance, ledgerBalance);
            }
            notiRequest.getPublicationBody().setAvailableBalanceSign(CommonUtil.getBalanceSign(availableBalance));
            notiRequest.getPublicationBody().setAvailableBalance(CommonUtil.safeAbs(availableBalance));
            notiRequest.getPublicationBody().setLedgerBalanceSign(CommonUtil.getBalanceSign(ledgerBalance));
            notiRequest.getPublicationBody().setLedgerBalance(CommonUtil.safeAbs(ledgerBalance));
        }
        log.info("[process-withdrawal-reversal] completed notification request: {}", notiRequest);
        return notiRequest;
    }

    private BigDecimal getBalance(DebitCreditResponse pymdResponse, String balanceType) {
        log.info("[process-withdrawal-reversal] extracting balance of type: {} from pymd response: {}", balanceType, pymdResponse);
        BigDecimal balance = pymdResponse.getFromAccount().getBalanceInfo().getAccountBalances().stream()
            .filter(b -> balanceType.equalsIgnoreCase(b.getBalanceType()))
            .map(b -> b.getAmount())
            .findFirst()
            .orElse(null);
        log.info("[process-withdrawal-reversal] extracted balance: {} for type: {}", balance, balanceType);
        return balance;
    }

    private DebitCreditRequest buildDebitCreditRequest(WithdrawalReversalRequest request, String originalRequestUid,
                                                       BigDecimal amount, String transactionType) {
        log.info("[process-withdrawal-reversal] building debit credit request for withdrawal reversal: {}, amount: {}, transaction type: {}",
                request, amount, transactionType);
        DebitCreditRequest debitCreditRequest = DebitCreditRequest.builder()
                .bankCode("14")
                .deviceId(request.getTerminalId())
                .financialType("ETFR")
                .originalRequestUid(originalRequestUid)
                .processingBranch(request.getTerminalBranchId())
                .terminalNumber(request.getTerminalId())
                .transactionType(transactionType)
                .fromAccount(AccountRequest.builder()
                        .accountCurrency("764")
                        .accountNumber(request.getFromAccountNumber())
                        .build())
                .remittanceInfo(RemittanceInfo.builder()
                        .amount(amount)
                        .feeChargeAcct("DEPACCTIDFROM")
                        .fees(
                            Arrays.asList(
                                Fees.builder()
                                .feeAmount(request.getAmounts().getFeeAmount())
                                .feeAmountCurrency("764")
                                .feeType("TRAN")
                                .build()
                            )
                        )
                        .build())
                .statementDescription(StatementDescription.builder()
                        .fromDepInfo("SCB".equals(request.getBankCardOwner()) ? request.getTerminalLocation() : request.getTerminalType().concat(request.getBankTerminalOwner()))
                        .build())
                .build();
        log.info("[process-withdrawal-reversal] built debit credit request: {}", debitCreditRequest);
        return debitCreditRequest;
    }

    private WithdrawalReversalResponse handleDuplicateTransactionForReversal() {
        log.info("[process-withdrawal-reversal] handling duplicate transaction for reversal");
        return WithdrawalReversalResponse.createErrorResponse(
                AtmResponseCodes.INVALID_ACCOUNT,  AtmResponseCodes.MSG_DUPLICATE_TRANSACTION);
    }

    private WithdrawalReversalResponse handleDuplicateCheckErrorForReversal() {
        log.info("[process-withdrawal-reversal] handling duplicate check error for reversal");
        return WithdrawalReversalResponse.createErrorResponse(
                AtmResponseCodes.DATABASE_ERROR,  AtmResponseCodes.getDescription(AtmResponseCodes.DATABASE_ERROR));
    }

    private WithdrawalReversalResponse handle71Error() {
        log.info("[process-withdrawal-reversal] handling 71 error");
        return WithdrawalReversalResponse.createErrorResponse(
                AtmResponseCodes.TRANSACTION_NOT_ALLOWED, AtmResponseCodes.getDescription(AtmResponseCodes.TRANSACTION_NOT_ALLOWED));
    }

    private String getOriginalSystem(WithdrawalReversalRequest request) {
        log.info("[process-withdrawal-reversal] determining original system for request: {}", request);
        String bankTerminalOwner = request.getBankTerminalOwner();
        String terminalId = request.getTerminalId();
        log.info("[process-withdrawal-reversal] bank terminal owner: {}, terminal id: {}", bankTerminalOwner, terminalId);

        String originalSystem;
        if ("SCB".equals(bankTerminalOwner)) {
            if (terminalId != null && terminalId.length() >= 6) {
                char position6 = terminalId.charAt(5);
                if (position6 == '6') {
                    originalSystem = "SCDM";
                } else {
                    originalSystem = "SATM";
                }
            } else {
                originalSystem = "SATM";
            }
        } else if ("MDS".equals(bankTerminalOwner)) {
            originalSystem = "IATM";
        } else {
            originalSystem = "OATM";
        }
        log.info("[process-withdrawal-reversal] determined original system: {}", originalSystem);
        return originalSystem;
    }
}
