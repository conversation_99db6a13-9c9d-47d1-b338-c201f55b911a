package com.scb.atmi.service;

import com.scb.atmi.constant.AtmResponseCodes;
import com.scb.atmi.enums.ResponseFrom;
import com.scb.atmi.library.communication.rest.RestServiceException;
import com.scb.atmi.library.dto.DuplicateCheckDto;
import com.scb.atmi.library.entity.TransactionLogEntity;
import com.scb.atmi.library.log.AppLogger;
import com.scb.atmi.library.log.LoggerFactory;
import com.scb.atmi.library.model.CardRefCheckResult;
import com.scb.atmi.library.model.DuplicateCheckResult;
import com.scb.atmi.library.model.constant.HeaderConstant;
import com.scb.atmi.library.model.request.coop.CoopRequest;
import com.scb.atmi.library.model.request.notification.NotiRequest;
import com.scb.atmi.library.model.request.notification.PublicationBody;
import com.scb.atmi.library.model.request.paymentDomain.DebitCreditRequest;
import com.scb.atmi.library.model.request.paymentDomain.debitCredit.AccountRequest;
import com.scb.atmi.library.model.request.paymentDomain.debitCredit.Fees;
import com.scb.atmi.library.model.request.paymentDomain.debitCredit.RemittanceInfo;
import com.scb.atmi.library.model.request.paymentDomain.debitCredit.StatementDescription;
import com.scb.atmi.library.model.response.coop.COOPMsg;
import com.scb.atmi.library.model.response.coop.CoopResponse;
import com.scb.atmi.library.model.response.paymentDomain.DebitCreditResponse;
import com.scb.atmi.library.model.response.paymentDomain.debitCredit.AccountBalance;
import com.scb.atmi.library.repository.TransactionLogRepository;
import com.scb.atmi.library.service.CardProfileService;
import com.scb.atmi.library.service.CoopService;
import com.scb.atmi.library.service.DuplicateCheckService;
import com.scb.atmi.library.service.PaymentDomainService;
import com.scb.atmi.library.service.async.AsyncAutoRetryService;
import com.scb.atmi.library.service.async.AsyncNotificationService;
import com.scb.atmi.library.service.cache.ResponseLookupService;
import com.scb.atmi.library.util.AtmUtil;
import com.scb.atmi.library.util.CardUtils;
import com.scb.atmi.model.request.WithdrawalTransactionRequest;
import com.scb.atmi.model.response.WithdrawalTransactionResponse;
import com.scb.atmi.util.COOPMsgGenerator;
import com.scb.atmi.util.CommonUtil;
import com.scb.atmi.util.DuplicateCheckMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.UUID;

import static com.scb.atmi.constant.CommonConstants.*;
import static com.scb.atmi.library.service.async.AsyncAutoRetryService.COOP;
import static com.scb.atmi.library.service.async.AsyncAutoRetryService.PYMD;

@Service
@RequiredArgsConstructor
public class WithdrawalService {
    private final AppLogger log = LoggerFactory.getAppLogger(this.getClass());
    private final DuplicateCheckService duplicateCheckService;
    private final CardProfileService cardProfileService;
    private final TransactionLogRepository transactionLogRepository;
    private final CoopService coopService;
    private final AsyncAutoRetryService asyncAutoRetryService;
    private final PaymentDomainService paymentDomainService;
    private final AsyncNotificationService asyncNotificationService;
    private final ResponseLookupService responseLookupService;

    public WithdrawalTransactionResponse processWithdrawalConfirm(HttpHeaders headers, WithdrawalTransactionRequest request) {
        log.info("[process-withdrawal-confirm] start process");
        String correlationId = headers.getFirst(HeaderConstant.CORRELATION_ID);

        log.info("[process-withdrawal-confirm] validating transaction request");
        WithdrawalTransactionResponse validationResponse = validateTransaction(request);
        if (validationResponse != null) {
            log.info("[process-withdrawal-confirm] validation failed, returning error response");
            return validationResponse;
        }

        log.info("[process-withdrawal-confirm] initializing transaction");
        initializeTransaction(request);

        log.info("[process-withdrawal-confirm] checking card reference");
        CardRefCheckResult cardRefResult = cardProfileService.checkCardRef(request.getCardReference(), request.getCardNumber());
        if (cardRefResult.getStatus() != CardRefCheckResult.Status.SUCCESS) {
            log.info("[process-withdrawal-confirm] card reference check failed: {}", cardRefResult.getMessage());
            if (cardRefResult.getStatus() == CardRefCheckResult.Status.ERROR_71) {
                return WithdrawalTransactionResponse.createErrorResponse(
                    AtmResponseCodes.INVALID_ACCOUNT, AtmResponseCodes.getDescription(AtmResponseCodes.INVALID_ACCOUNT));
            }
            return WithdrawalTransactionResponse.createErrorResponse(
                AtmResponseCodes.DATABASE_ERROR, AtmResponseCodes.getDescription(AtmResponseCodes.DATABASE_ERROR));
        }
        request.setCardNumber(cardRefResult.getCardRef());

        log.info("[process-withdrawal-confirm] checking duplicate transactions");
        // Check for duplicate transactions using new DTO approach
        DuplicateCheckDto duplicateCheckDto = DuplicateCheckMapper.fromWithdrawalRequest(request, false);
        DuplicateCheckResult duplicateCheck =
            duplicateCheckService.checkDuplicateTransaction(duplicateCheckDto);

        if (duplicateCheck.isError()) {
            log.info("[process-withdrawal-confirm] duplicate check error occurred");
            return handleDuplicateCheckError();
        }

        if (duplicateCheck.isDuplicate()) {
            log.info("[process-withdrawal-confirm] duplicate transaction detected");
            return handleDuplicateTransaction();
        }

        log.info("[process-withdrawal-confirm] inserting transaction log");
        TransactionLogEntity respTransLogInsert = this.insertTransactionLog(request, TRANSACTION_STATUS_WITHDRAWAL_IN_PROGRESS);
        WithdrawalTransactionResponse response = null;
        try {
            response = processWithdrawalByCardType(request, correlationId);
            return response;
        } catch (Exception e) {
            log.error("[process-withdrawal-confirm] error occurred: {}", e.getMessage());
            return WithdrawalTransactionResponse.createErrorResponse(
                AtmResponseCodes.TRANSACTION_NOT_ALLOWED,
                AtmResponseCodes.getDescription(AtmResponseCodes.TRANSACTION_NOT_ALLOWED)
            );
        } finally {
            if (response != null && AtmResponseCodes.APPROVED.equals(response.getResponseCode())) {
                respTransLogInsert.setTransactionStatus(TRANSACTION_STATUS_WITHDRAWAL_SUCCESS);
            } else {
                if (AtmResponseCodes.DESTINATION_NOT_AVAILABLE.equals(response.getResponseCode())) respTransLogInsert.setTimeoutFlag(true);
                respTransLogInsert.setTransactionStatus(TRANSACTION_STATUS_WITHDRAWAL_FAILED);
            }
            updateTransactionLog(respTransLogInsert);
        }

    }

    private WithdrawalTransactionResponse processWithdrawalByCardType(WithdrawalTransactionRequest request, String correlationId) {
        log.info("[process-withdrawal-confirm] processing withdrawal by card type for request: {}", request);
        if (isGiftCard(request.getCardType())) {
            log.info("[process-withdrawal-confirm] processing gift card withdrawal");
            // COBOL: B24SEM-CARD-TYPE = '25' OR '19' (line 1008)
            return returnGiftCardWithdrawal();
        } else if (isCoopCard(request)) {
            log.info("[process-withdrawal-confirm] processing coop card withdrawal");
            return processCoopWithdrawal(request, correlationId);

        } else {
            if (isOwnerTerm(request)) {
                log.info("[process-withdrawal-confirm] owner terminal detected, returning external error");
                return WithdrawalTransactionResponse.createErrorResponse(
                    AtmResponseCodes.TRANSACTION_NOT_ALLOWED, AtmResponseCodes.getDescription(AtmResponseCodes.TRANSACTION_NOT_ALLOWED));
            }
            if (isFastCash(request)) {
                log.info("[process-withdrawal-confirm] processing fast cash withdrawal");
                // call post v4 debit credit
                return processWithdrawalTransactionFastCash(request, correlationId);
            } else {
                log.info("[process-withdrawal-confirm] processing non-fast cash withdrawal");
                return processWithdrawalNonFastCash(request, correlationId);
            }
        }
    }

    private WithdrawalTransactionResponse processCoopWithdrawal(WithdrawalTransactionRequest request, String correlationId) {
        log.info("[withdrawal-confirm-coop] starting coop withdrawal processing for request: {}", request);
        try {
            CoopRequest coopRequest = buildCoopRequest(request, COOP_MSG_TYPE_RQST);
            log.info("[withdrawal-confirm-coop] built coop request: {}", coopRequest);
            CoopResponse coopResponse = coopService.callReverseService(coopRequest, correlationId);
            log.info("[withdrawal-confirm-coop] received coop response: {}", coopResponse);
            COOPMsg coopMsg = coopResponse.getCOOPMsg();
            String statusCode = coopResponse.getDocRs().getMerchantCOOPRs().getStatus().getStatusCode();
            if (COOP_STATUS_CODE_SUCCESS.equals(statusCode) && COOP_RESPONSE_CODE_SUCCESS.equals(coopMsg.getResponseCode())) {
                String atmTransactionId = duplicateCheckService.generateTransactionLogKey(
                    DuplicateCheckMapper.fromWithdrawalRequest(request, false)
                );
                log.info("[withdrawal-confirm-coop] coop - generated transaction id: {}", atmTransactionId);
                NotiRequest notiRequest = NotiRequest.createNotiRequest("01",
                        atmTransactionId,
                        PublicationBody.builder()
                                .messageType("RQST")
                                .cardMaskedNumber(CardUtils.maskCardNumber(request.getCardNumber())) // Mask card number
                                .transactionAmount(request.getAmounts().getTotalAmount())
                                .transactionFee(request.getAmounts().getFeeAmount())
                                .currencyCode("274")
                                .toAccount(request.getToAccountNumber())
                                .build());
                asyncNotificationService.sendNotificationAsync(notiRequest, correlationId);
            } else if (COOP_STATUS_CODE_SUCCESS.equals(statusCode) && !COOP_RESPONSE_CODE_SUCCESS.equals(coopMsg.getResponseCode())) {
                return WithdrawalTransactionResponse.createErrorResponse(
                    coopMsg.getResponseCode(),
                    coopMsg.getResponseDesc()
                );
            } else {
                return WithdrawalTransactionResponse.createErrorResponse(
                    AtmResponseCodes.TRANSACTION_NOT_ALLOWED,
                    AtmResponseCodes.getDescription(AtmResponseCodes.TRANSACTION_NOT_ALLOWED)
                );
            }
            log.info("[withdrawal-confirm-coop] returning success response");
            return WithdrawalTransactionResponse.createSuccessResponseCaseCoop(BigDecimal.ZERO, BigDecimal.ZERO);
        } catch (RestServiceException e) {
            log.error("[withdrawal-confirm-coop] timeout error occurred: {}", e);
            return WithdrawalTransactionResponse.createErrorResponse(
                AtmResponseCodes.DESTINATION_NOT_AVAILABLE,
                AtmResponseCodes.getDescription(AtmResponseCodes.DESTINATION_NOT_AVAILABLE)
            );
        } catch (Exception e) {
            log.error("[withdrawal-confirm-coop] unexpected error occurred: {}", e.getMessage(), e);
            return WithdrawalTransactionResponse.createErrorResponse(
                AtmResponseCodes.TRANSACTION_NOT_ALLOWED,
                AtmResponseCodes.getDescription(AtmResponseCodes.TRANSACTION_NOT_ALLOWED)
            );
        }
    }

    private WithdrawalTransactionResponse processWithdrawalNonFastCash(WithdrawalTransactionRequest request, String correlationId) {
        log.info("[process-withdrawal-confirm] starting non-fast cash withdrawal processing for request: {}", request);
        try {
            return processSaving(request, correlationId);
        } catch (RestServiceException e) {
            log.error("[process-withdrawal-non-fast-cash] error occurred: {}", e);
            String errorCode = AtmUtil.handleClientError(e);
            if ("JMS01".equals(errorCode) || AtmResponseCodes.DESTINATION_NOT_AVAILABLE.equals(errorCode)) {
                log.info("[process-withdrawal-non-fast-cash] JMS01 error detected in 504 response");
                return autoRetryPymd(request, true, correlationId);
            } else {
                String responseCode = responseLookupService.getResponseCode(
                    request.getProcessingCode().getTransactionType(),
                    ResponseFrom.PYMD.getValue(),
                    errorCode
                );
                return WithdrawalTransactionResponse.createErrorResponse(
                    responseCode,
                    AtmResponseCodes.getDescription(responseCode)
                );
            }
        } catch (Exception e) {
            log.error("[process-withdrawal-confirm] exception error: {}", e.getMessage(), e);
            return WithdrawalTransactionResponse.createErrorResponse(AtmResponseCodes.DESTINATION_NOT_AVAILABLE, AtmResponseCodes.getDescription(AtmResponseCodes.DESTINATION_NOT_AVAILABLE));
        }
    }

    private WithdrawalTransactionResponse processWithdrawalTransactionFastCash(WithdrawalTransactionRequest request, String correlationId) {
        log.info("[process-withdrawal-confirm] starting fast cash withdrawal processing for request: {}", request);
        try {
            return processSaving(request, correlationId);
        } catch (RestServiceException e) {
            log.error("[withdrawal-transaction-fast-cash] error occurred: {}", e);
            String errorCode = AtmUtil.handleClientError(e);
            if ("JMS01".equals(errorCode) || AtmResponseCodes.DESTINATION_NOT_AVAILABLE.equals(errorCode)) {
                log.info("[withdrawal-transaction-fast-cash] JMS01 error detected in 504 response");
                autoRetryPymd(request, true, correlationId);
                return processCurrent(request, correlationId);
            } else {
                String responseCode = responseLookupService.getResponseCode(
                    request.getProcessingCode().getTransactionType(),
                    ResponseFrom.PYMD.getValue(),
                    errorCode
                );
                return WithdrawalTransactionResponse.createErrorResponse(
                    responseCode,
                    AtmResponseCodes.getDescription(responseCode)
                );
            }
        } catch (Exception e) {
            log.error("[process-withdrawal-confirm] unexpected error occurred: {}", e.getMessage(), e);
            return WithdrawalTransactionResponse.createErrorResponse(
                AtmResponseCodes.TRANSACTION_NOT_ALLOWED,
                AtmResponseCodes.getDescription(AtmResponseCodes.TRANSACTION_NOT_ALLOWED)
            );
        }
    }

    private WithdrawalTransactionResponse processCurrent(WithdrawalTransactionRequest request, String correlationId) {
        log.info("[process-withdrawal-confirm] starting current account withdrawal processing for request: {}", request);
        log.info("[withdrawal-process-current] Processing saving account withdrawal");
        try {
            DebitCreditRequest debitCreditRequest = buildDebitCreditRequest(
                    request,
                    "",
                    request.getAmounts().getTotalAmount(),
                    false,
                    PYMD_TRANSACTION_DEBIT_TYPE
            );
            log.info("[process-withdrawal-confirm] built debit credit request: {}", debitCreditRequest);
            DebitCreditResponse pymdResponse = paymentDomainService.postDebitCreditServiceInfo(
                debitCreditRequest,
                getOriginalSystem(request),
                correlationId
            );

            log.info("[withdrawal-process-current] pymd response: {}", pymdResponse);
            AccountBalance filterResponseAvailBalance = null;
            AccountBalance filterResponseBalance = null;
            if (checkResponseIsNull(pymdResponse)) {
                filterResponseAvailBalance = filterPymdResponseAvailBalance(pymdResponse);
                filterResponseBalance = filterPymdResponseBalance(pymdResponse);
                log.info("[process-withdrawal-confirm] extracted balances - available: {}, ledger: {}",
                        filterResponseAvailBalance != null ? filterResponseAvailBalance.getAmount() : null,
                        filterResponseBalance != null ? filterResponseBalance.getAmount() : null);
            }
            NotiRequest notiRequest = buildNotiRequest(request, false, null, pymdResponse);
            asyncNotificationService.sendNotificationAsync(notiRequest, correlationId);
            log.info("[process-withdrawal-confirm] returning success response");
            return WithdrawalTransactionResponse.createSuccessResponseCasePymd(filterResponseAvailBalance.getAmount(), filterResponseBalance.getAmount());
        } catch (RestServiceException e) {
            log.error("[withdrawal-process-current] error occurred: {}", e);
            String errorCode = AtmUtil.handleClientError(e);
            if ("JMS01".equals(errorCode) || AtmResponseCodes.DESTINATION_NOT_AVAILABLE.equals(errorCode)) {
                return autoRetryPymd(request, false, correlationId);
            } else {
                String responseCode = responseLookupService.getResponseCode(
                    request.getProcessingCode().getTransactionType(),
                    ResponseFrom.PYMD.getValue(),
                    errorCode
                );
                return WithdrawalTransactionResponse.createErrorResponse(
                    responseCode,
                    AtmResponseCodes.getDescription(responseCode)
                );
            }
        } catch (Exception e) {
            log.error("[process-withdrawal-confirm] unexpected error occurred: {}", e.getMessage(), e);
            return WithdrawalTransactionResponse.createErrorResponse(
                AtmResponseCodes.TRANSACTION_NOT_ALLOWED,
                AtmResponseCodes.getDescription(AtmResponseCodes.TRANSACTION_NOT_ALLOWED)
            );
        }
    }

    private WithdrawalTransactionResponse processSaving(WithdrawalTransactionRequest request, String correlationId) {
        log.info("[process-withdrawal-confirm] starting saving account withdrawal processing for request: {}", request);
        log.info("[withdrawal-process-saving] Processing saving account withdrawal");

        DebitCreditRequest debitCreditRequest = buildDebitCreditRequest(
                request,
                "",
                request.getAmounts().getTotalAmount(),
                true,
                PYMD_TRANSACTION_DEBIT_TYPE
        );
        log.info("[process-withdrawal-confirm] built debit credit request: {}", debitCreditRequest);
        DebitCreditResponse pymdResponse = paymentDomainService.postDebitCreditServiceInfo(
            debitCreditRequest,
            getOriginalSystem(request),
            correlationId
        );

        log.info("[withdrawal-process-saving] pymd response: {}", pymdResponse);
        AccountBalance filterResponseAvailBalance = null;
        AccountBalance filterResponseBalance = null;
        if (checkResponseIsNull(pymdResponse)) {
            filterResponseAvailBalance = filterPymdResponseAvailBalance(pymdResponse);
            filterResponseBalance = filterPymdResponseBalance(pymdResponse);
            log.info("[process-withdrawal-confirm] extracted balances - available: {}, ledger: {}",
                    filterResponseAvailBalance != null ? filterResponseAvailBalance.getAmount() : null,
                    filterResponseBalance != null ? filterResponseBalance.getAmount() : null);
        }
        NotiRequest notiRequest = buildNotiRequest(request, false, null, pymdResponse);
        asyncNotificationService.sendNotificationAsync(notiRequest,correlationId);

        log.info("[process-withdrawal-confirm] returning success response");
        return WithdrawalTransactionResponse.createSuccessResponseCasePymd(filterResponseAvailBalance.getAmount(), filterResponseBalance.getAmount());
    }

    private AccountBalance filterPymdResponseAvailBalance(DebitCreditResponse pymdResponse) {
        log.info("[process-withdrawal-confirm] filtering available balance from pymd response: {}", pymdResponse);
        AccountBalance availBalance = pymdResponse.getFromAccount().getBalanceInfo().getAccountBalances()
            .stream()
            .filter(b -> PYMD_ACCOUNT_BALANCE_TYPE_AVAILBALANCE.equalsIgnoreCase(b.getBalanceType()))
            .findFirst()
            .orElse(new AccountBalance());
        log.info("[process-withdrawal-confirm] filtered available balance: {}", availBalance);
        return availBalance;
    }

    private AccountBalance filterPymdResponseBalance(DebitCreditResponse pymdResponse) {
        log.info("[process-withdrawal-confirm] filtering ledger balance from pymd response: {}", pymdResponse);
        AccountBalance ledgerBalance = pymdResponse.getFromAccount().getBalanceInfo().getAccountBalances()
            .stream()
            .filter(b -> PYMD_ACCOUNT_BALANCE_TYPE_BALANCE.equalsIgnoreCase(b.getBalanceType()))
            .findFirst()
            .orElse(new AccountBalance());
        log.info("[process-withdrawal-confirm] filtered ledger balance: {}", ledgerBalance);
        return ledgerBalance;
    }

    private WithdrawalTransactionResponse autoRetryPymd(WithdrawalTransactionRequest request, boolean isSaving, String correlationId) {
        log.info("[process-withdrawal-confirm] starting auto retry for pymd request: {}, is saving: {}", request, isSaving);
        DebitCreditRequest debitCreditRequest = buildDebitCreditRequest(
                request,
                "",
                request.getAmounts().getTotalAmount(),
                isSaving,
                PYMD_TRANSACTION_REVDEBIT_TYPE
        );
        log.info("[process-withdrawal-confirm] built debit credit request for retry: {}", debitCreditRequest);
        NotiRequest notiRequest = buildNotiRequest(request, true,null, null);
        asyncAutoRetryService.executeAsyncAutoRetryService(debitCreditRequest, null, getOriginalSystem(request),
                correlationId, PYMD, notiRequest);
        log.info("[process-withdrawal-confirm] initiated async auto retry service");
        return WithdrawalTransactionResponse.createErrorResponse(
            AtmResponseCodes.DESTINATION_NOT_AVAILABLE, AtmResponseCodes.getDescription(AtmResponseCodes.DESTINATION_NOT_AVAILABLE));
    }

    private WithdrawalTransactionResponse autoRetryCoop(WithdrawalTransactionRequest request, String correlationId) {
        log.info("[process-withdrawal-confirm] starting auto retry for coop request: {}", request);
        CoopRequest coopRequest = buildCoopRequest(request, COOP_MSG_TYPE_RVSL);
        log.info("[process-withdrawal-confirm] built coop request for retry: {}", coopRequest);
        NotiRequest notiRequest = buildNotiRequest(request, true,null, null);
        asyncAutoRetryService.executeAsyncAutoRetryService(null, coopRequest, getOriginalSystem(request),
                correlationId, COOP, notiRequest);
        log.info("[process-withdrawal-confirm] initiated async auto retry service");
        return WithdrawalTransactionResponse.createErrorResponse(
            AtmResponseCodes.DESTINATION_NOT_AVAILABLE, AtmResponseCodes.getDescription(AtmResponseCodes.DESTINATION_NOT_AVAILABLE));
    }

    private BigDecimal getBalance(DebitCreditResponse pymdResponse, String balanceType) {
        log.info("[process-withdrawal-confirm] extracting balance of type: {} from pymd response: {}", balanceType, pymdResponse);
        BigDecimal balance = pymdResponse.getFromAccount().getBalanceInfo().getAccountBalances().stream()
                .filter(b -> balanceType.equalsIgnoreCase(b.getBalanceType()))
                .map(b -> b.getAmount())
                .findFirst()
                .orElse(null);
        log.info("[process-withdrawal-confirm] extracted balance: {} for type: {}", balance, balanceType);
        return balance;
    }

    private NotiRequest buildNotiRequest(WithdrawalTransactionRequest request,
                                         boolean isReversal,
                                         CoopResponse coopResponse,
                                         DebitCreditResponse debitCreditResponse) {
        log.info("[process-withdrawal-confirm] building notification request for withdrawal: {}, is reversal: {}", request, isReversal);
        String transactionLogKey = duplicateCheckService.generateTransactionLogKey(
                DuplicateCheckMapper.fromWithdrawalRequest(request, request.getOriginalMessageType() != null)
        );
        log.info("[process-withdrawal-confirm] generated transaction log key: {}", transactionLogKey);
        String subEventCode = "01";
        String messageType = "RQST";
        if (isReversal) {
            subEventCode = "02";
            messageType = "RVSL";
        }
        log.info("[process-withdrawal-confirm] sub event code: {}, message type: {}", subEventCode, messageType);
        NotiRequest notiRequest = NotiRequest.createNotiRequest(subEventCode,
                transactionLogKey,
                PublicationBody.builder()
                        .messageType(messageType)
                        .cardMaskedNumber(CardUtils.maskCardNumber(request.getCardNumber())) // Mask card number
                        .transactionAmount(request.getAmounts().getTotalAmount())
                        .transactionFee(request.getAmounts().getFeeAmount())
                        .currencyCode("764")
                        .fromAccount(request.getFromAccountNumber())
                        .toAccount(request.getToAccountNumber())
                        .build());
        log.info("[process-withdrawal-confirm] created base notification request: {}", notiRequest);
        if (coopResponse != null) {
            log.info("[process-withdrawal-confirm] processing coop response: {}", coopResponse);
            // TODO: set coop response to noti request
        } else if (debitCreditResponse != null) {
            log.info("[process-withdrawal-confirm] processing debit credit response: {}", debitCreditResponse);
            BigDecimal availableBalance = null;
            BigDecimal ledgerBalance = null;
            if (checkResponseIsNull(debitCreditResponse)) {
                availableBalance = getBalance(debitCreditResponse, PYMD_ACCOUNT_BALANCE_TYPE_AVAILBALANCE);
                ledgerBalance = getBalance(debitCreditResponse, PYMD_ACCOUNT_BALANCE_TYPE_BALANCE);
                log.info("[process-withdrawal-confirm] extracted balances - available: {}, ledger: {}", availableBalance, ledgerBalance);
            }
            notiRequest.getPublicationBody().setAvailableBalanceSign(CommonUtil.getBalanceSign(availableBalance));
            notiRequest.getPublicationBody().setAvailableBalance(CommonUtil.safeAbs(availableBalance));
            notiRequest.getPublicationBody().setLedgerBalanceSign(CommonUtil.getBalanceSign(ledgerBalance));
            notiRequest.getPublicationBody().setLedgerBalance(CommonUtil.safeAbs(ledgerBalance));
        }
        log.info("[process-withdrawal-confirm] completed notification request: {}", notiRequest);
        return notiRequest;
    }

    private boolean checkResponseIsNull(DebitCreditResponse debitCreditResponse) {
        log.info("[process-withdrawal-confirm] checking if debit credit response has valid balance info: {}", debitCreditResponse);
        boolean hasValidBalanceInfo = debitCreditResponse.getFromAccount() != null &&
            debitCreditResponse.getFromAccount().getBalanceInfo() != null &&
            debitCreditResponse.getFromAccount().getBalanceInfo().getAccountBalances() != null;
        log.info("[process-withdrawal-confirm] has valid balance info: {}", hasValidBalanceInfo);
        return hasValidBalanceInfo;
    }

    private CoopRequest buildCoopRequest(WithdrawalTransactionRequest request, String msgType){
        log.info("[process-withdrawal-confirm] building coop request for withdrawal: {}", request);
        String uuid = UUID.randomUUID().toString().replace("-", "");
        log.info("[process-withdrawal-confirm] generated UUID: {}", uuid);
        String coopMsg = COOPMsgGenerator.generateCOOPMsg(request, msgType);
        log.info("[process-withdrawal-confirm] generated coop message: {}", coopMsg);
        CoopRequest coopRequest = CoopRequest.builder().docRq(CoopRequest.DocRq.builder()
                        .merchantCOOPRq(CoopRequest.DocRq.MerchantCOOPRq.builder()
                                .rqUID(uuid)
                                .tranType("KUSD")
                                .coopMsg(coopMsg)
                                .build())
                        .build())
            .build();
        log.info("[process-withdrawal-confirm] built coop request: {}", coopRequest);
        return coopRequest;
    }

    private DebitCreditRequest buildDebitCreditRequest(
        WithdrawalTransactionRequest request,
        String originalRequestUid,
        BigDecimal amount,
        boolean isSaving,
        String transactionType
    ) {
        log.info("[process-withdrawal-confirm] building debit credit request for withdrawal: {}, amount: {}, is saving: {}, transaction type: {}",
                request, amount, isSaving, transactionType);
        DebitCreditRequest debitCreditRequest = DebitCreditRequest.builder()
            .bankCode("14")
            .deviceId(request.getTerminalId())
            .financialType("ETFR")
            .originalRequestUid(originalRequestUid)
            .processingBranch(request.getTerminalBranchId())
            .terminalNumber(request.getTerminalId())
            .transactionType(transactionType)
            .fromAccount(AccountRequest.builder()
                .accountCurrency("764")
                .accountNumber(isSaving ? request.getFromAccountNumber() : request.getToAccountNumber())
                .build())
            .remittanceInfo(RemittanceInfo.builder()
                .amount(amount)
                .feeChargeAcct("DEPACCTIDFROM")
                .fees(Arrays.asList(
                    Fees.builder()
                        .feeAmount(request.getAmounts().getFeeAmount())
                        .feeAmountCurrency("764")
                        .feeType("TRAN")
                        .build()
                ))
                .build())
            .statementDescription(StatementDescription.builder()
                .fromDepInfo("SCB".equals(request.getBankCardOwner()) ? request.getTerminalLocation() : request.getTerminalType().concat(request.getBankTerminalOwner()))
                .build())
            .build();
        log.info("[process-withdrawal-confirm] built debit credit request: {}", debitCreditRequest);
        return debitCreditRequest;
    }

    private boolean isFastCash(WithdrawalTransactionRequest request) {
        log.info("[process-withdrawal-confirm] checking if request is fast cash for request: {}", request);
        String processingCode = request.getProcessingCode().getTransactionType()
            .concat(request.getProcessingCode().getFromAccountType())
            .concat(request.getProcessingCode().getToAccountType());
        boolean isFastCash = "101101".equals(processingCode);
        log.info("[process-withdrawal-confirm] processing code: {}, is fast cash: {}", processingCode, isFastCash);
        return isFastCash;
    }

    private boolean isOwnerTerm(WithdrawalTransactionRequest request) {
        log.info("[process-withdrawal-confirm] checking if request is owner terminal for request: {}", request);
        boolean isOwnerTerm = !isScbTerminal(request.getBankCardOwner()) && !isDivisibleBy100(request.getAmounts().getTotalAmount());
        log.info("[process-withdrawal-confirm] bank card owner: {}, amount: {}, is owner term: {}",
                request.getBankCardOwner(), request.getAmounts().getTotalAmount(), isOwnerTerm);
        return isOwnerTerm;
    }

    public boolean isDivisibleBy100(BigDecimal number) {
        log.info("[process-withdrawal-confirm] checking if number is divisible by 100: {}", number);
        BigDecimal hundred = new BigDecimal("100");
        boolean isDivisible = number.remainder(hundred).compareTo(BigDecimal.ZERO) == 0;
        log.info("[process-withdrawal-confirm] is divisible by 100: {}", isDivisible);
        return isDivisible;
    }

    private boolean isScbTerminal(String cardOwner) {
        log.info("[process-withdrawal-confirm] checking if card owner is SCB terminal: {}", cardOwner);
        boolean isScbTerminal = "SCB".equals(cardOwner) || "MDS".equals(cardOwner) || "VISA".equals(cardOwner);
        log.info("[process-withdrawal-confirm] is SCB terminal: {}", isScbTerminal);
        return isScbTerminal;
    }

    private boolean isCoopCard(WithdrawalTransactionRequest request) {
        log.info("[process-withdrawal-confirm] checking if card is coop card for request: {}", request);
        boolean isCoopCard = "18".equals(request.getCardType()) && "01".equals(request.getProcessingCode().getFromAccountType());
        log.info("[process-withdrawal-confirm] card type: {}, from account type: {}, is coop card: {}",
                request.getCardType(), request.getProcessingCode().getFromAccountType(), isCoopCard);
        return isCoopCard;
    }

    public WithdrawalTransactionResponse validateTransaction(WithdrawalTransactionRequest request) {
        String transactionType = request.getProcessingCode().getTransactionType();
        String fromAccountType = request.getProcessingCode().getFromAccountType();

        // Validate terminal ID
        if (!TRANSACTION_TYPE_ALLOWED.equals(transactionType)) {
            return WithdrawalTransactionResponse.createErrorResponse(
                AtmResponseCodes.MISSING_INVALID_REQUEST, AtmResponseCodes.getDescription(AtmResponseCodes.MISSING_INVALID_REQUEST));
        }

        if (FIXE_ACCOUNT_NOT_ALLOWED.equals(fromAccountType)) {
            return WithdrawalTransactionResponse.createErrorResponse(
                AtmResponseCodes.INVALID_ACCOUNT, AtmResponseCodes.getDescription(AtmResponseCodes.INVALID_ACCOUNT));
        }

        if (request.getAmounts().getTotalAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return WithdrawalTransactionResponse.createErrorResponse(
                AtmResponseCodes.MISSING_INVALID_REQUEST, AtmResponseCodes.getDescription(AtmResponseCodes.MISSING_INVALID_REQUEST));
        }
        return null;
    }

    public void initializeTransaction(WithdrawalTransactionRequest request) {
        // Set default values if not provided
        if (request.getTransactionDate() == null) {
            request.setTransactionDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        }

        if (request.getTransactionTime() == null) {
            request.setTransactionTime(LocalTime.now().format(DateTimeFormatter.ofPattern("HHmmss")));
        }

        if (request.getPostingDate() == null) {
            request.setPostingDate(request.getTransactionDate());
        }
    }

    private WithdrawalTransactionResponse handleDuplicateTransaction() {
        log.info("[process-withdrawal-confirm] handling duplicate transaction");
        return WithdrawalTransactionResponse.createErrorResponse(
            AtmResponseCodes.INVALID_ACCOUNT,  AtmResponseCodes.MSG_DUPLICATE_TRANSACTION);
    }

    private WithdrawalTransactionResponse handleDuplicateCheckError() {
        log.info("[process-withdrawal-confirm] handling duplicate check error");
        return WithdrawalTransactionResponse.createErrorResponse(
            AtmResponseCodes.DATABASE_ERROR,  AtmResponseCodes.getDescription(AtmResponseCodes.DATABASE_ERROR));
    }

    private boolean isGiftCard(String accountType) {
        log.info("[process-withdrawal-confirm] checking if account type is gift card: {}", accountType);
        // Gift card account types
        boolean isGiftCard = "25".equals(accountType) || "19".equals(accountType);
        log.info("[process-withdrawal-confirm] is gift card: {}", isGiftCard);
        return isGiftCard;
    }

    private WithdrawalTransactionResponse returnGiftCardWithdrawal() {
        log.info("[process-withdrawal-confirm] returning gift card withdrawal error response");
        return WithdrawalTransactionResponse.createErrorResponse(
            AtmResponseCodes.TRANSACTION_NOT_ALLOWED, AtmResponseCodes.getDescription(AtmResponseCodes.TRANSACTION_NOT_ALLOWED) );
    }

    @Transactional
    public TransactionLogEntity insertTransactionLog(WithdrawalTransactionRequest request, String status) {
        String transactionLogKey = duplicateCheckService.generateTransactionLogKey(
            DuplicateCheckMapper.fromWithdrawalRequest(request, false)
        );
        log.info("Inserting transaction log: {}", request);
        TransactionLogEntity transactionLog = TransactionLogEntity.builder()
            .atmTransactionId(transactionLogKey)
            .bankTerminalOwner(request.getBankTerminalOwner())
            .terminalId(request.getTerminalId())
            .sequenceNo(request.getTerminalSequence())
            .cardType(request.getCardType())
            .cardRef(request.getTerminalSequence())
            .cardMaskNo(request.getCardNumber())
            .fromAccount(request.getFromAccountNumber())
            .toAccount(request.getToAccountNumber())
            .messageType(request.getMessageType())
            .originalMessageType(request.getOriginalMessageType())
            .transactionType(request.getProcessingCode().getTransactionType())
            .transactionStatus(status)
            .transactionDate(request.getTransactionDate())
            .transactionTime(request.getTransactionTime())
            .postingDate(request.getPostingDate())
            .processingCode(getProcessingCode(request))
            .transactionAmount(request.getAmounts().getTotalAmount())
            .transactionFee(request.getAmounts().getFeeAmount())
            .currency(request.getCurrencyCode())
            .createdDate(LocalDateTime.now())
            .updatedDate(LocalDateTime.now())
            .build();
        return transactionLogRepository.save(transactionLog);
    }

    @Transactional
    public void updateTransactionLog(TransactionLogEntity transactionLog) {
        log.info("Update transaction log: {}", transactionLog);
        transactionLog.setUpdatedDate(LocalDateTime.now());
        transactionLogRepository.save(transactionLog);
    }


    private String getProcessingCode(WithdrawalTransactionRequest request) {
        log.info("[process-withdrawal-confirm] generating processing code for request: {}", request);
        String processingCode = request.getProcessingCode().getTransactionType() + request.getProcessingCode().getFromAccountType() + request.getProcessingCode().getToAccountType();
        log.info("[process-withdrawal-confirm] generated processing code: {}", processingCode);
        return processingCode;
    }

    private String getOriginalSystem(WithdrawalTransactionRequest request) {
        log.info("[process-withdrawal-confirm] determining original system for request: {}", request);
        String bankTerminalOwner = request.getBankTerminalOwner();
        String terminalId = request.getTerminalId();
        log.info("[process-withdrawal-confirm] bank terminal owner: {}, terminal id: {}", bankTerminalOwner, terminalId);

        String originalSystem;
        if ("SCB".equals(bankTerminalOwner)) {
            if (terminalId != null && terminalId.length() >= 6) {
                char position6 = terminalId.charAt(5);
                if (position6 == '6') {
                    originalSystem = "SCDM";
                } else {
                    originalSystem = "SATM";
                }
            } else {
                originalSystem = "SATM";
            }
        } else if ("MDS".equals(bankTerminalOwner)) {
            originalSystem = "IATM";
        } else {
            originalSystem = "OATM";
        }
        log.info("[process-withdrawal-confirm] determined original system: {}", originalSystem);
        return originalSystem;
    }
}
