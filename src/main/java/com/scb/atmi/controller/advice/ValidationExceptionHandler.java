package com.scb.atmi.controller.advice;

import com.scb.atmi.constant.AtmResponseCodes;
import com.scb.atmi.library.log.AppLogger;
import com.scb.atmi.library.log.LoggerFactory;
import com.scb.atmi.model.RespError;
import jakarta.validation.ValidationException;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
@Order(Ordered.HIGHEST_PRECEDENCE)
public class ValidationExceptionHandler {
    private final AppLogger log = LoggerFactory.getAppLogger(this.getClass());

    @ExceptionHandler(value = {ValidationException.class, MethodArgumentNotValidException.class})
    protected ResponseEntity<?> handleValidateException(Exception ex) {
        this.log.error("Handle Business Exception {}", ex.getMessage());
        RespError errorResponse =
            RespError.createErrorResponse(AtmResponseCodes.MISSING_INVALID_REQUEST, AtmResponseCodes.getDescription(AtmResponseCodes.MISSING_INVALID_REQUEST));

        return ResponseEntity.ok(errorResponse);
    }
}
