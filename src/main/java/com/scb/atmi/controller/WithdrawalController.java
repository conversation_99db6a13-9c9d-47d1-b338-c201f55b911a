package com.scb.atmi.controller;

import com.scb.atmi.library.log.AppLogger;
import com.scb.atmi.library.log.LoggerFactory;
import com.scb.atmi.model.request.WithdrawalReversalRequest;
import com.scb.atmi.model.request.WithdrawalTransactionRequest;
import com.scb.atmi.model.response.WithdrawalReversalResponse;
import com.scb.atmi.model.response.WithdrawalTransactionResponse;
import com.scb.atmi.service.WithdrawalReversalService;
import com.scb.atmi.service.WithdrawalService;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import org.springframework.http.HttpHeaders;

@RestController
@RequiredArgsConstructor
@RequestMapping("v1/withdrawal")
public class WithdrawalController {
    private final AppLogger log = LoggerFactory.getAppLogger(this.getClass());
    private final WithdrawalService withdrawalService;
    private final WithdrawalReversalService withdrawalReversalService;

    @PostMapping("/confirm")
    public ResponseEntity<WithdrawalTransactionResponse> processConfirm(
        @RequestHeader HttpHeaders headers,
        @Parameter(description = "Withdrawal transaction request", required = true)
        @Valid @RequestBody WithdrawalTransactionRequest request) {
        WithdrawalTransactionResponse response = withdrawalService.processWithdrawalConfirm(headers, request);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/reversal")
    public ResponseEntity<WithdrawalReversalResponse> processReversal(
            @RequestHeader HttpHeaders headers,
            @Parameter(description = "Withdrawal Reversal request", required = true)
            @Valid @RequestBody WithdrawalReversalRequest request) {
        WithdrawalReversalResponse response = withdrawalReversalService.processWithdrawalReversal(headers, request);
        return ResponseEntity.ok(response);
    }
}
