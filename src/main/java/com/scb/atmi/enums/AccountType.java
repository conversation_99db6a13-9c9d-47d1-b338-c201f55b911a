package com.scb.atmi.enums;

/**
 * Account Type Enum
 *
 * Defines valid account types for ATM transactions.
 *
 * <AUTHOR> ATM Migration Team
 * @since 2025-07-23
 */
public enum AccountType {
    CURRENT("01"),
    SAVINGS("11"),
    CHECKING("12"),
    FIXED("13");

    private String value;

    AccountType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
