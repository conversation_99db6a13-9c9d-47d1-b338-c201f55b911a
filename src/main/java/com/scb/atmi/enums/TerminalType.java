package com.scb.atmi.enums;

/**
 * Terminal Type Enum
 *
 * Defines valid terminal types for ATM transactions.
 *
 * <AUTHOR> ATM Migration Team
 * @since 2025-07-23
 */
public enum TerminalType {
    FOREIGN_ATM("00"),
    OTHER_BANK_LOCAL_1("01"),
    IBM_TERMINAL("03"),
    FUJISU_TERMINAL("08"),
    OTHER_BANK_LOCAL_2("11"),
    OTHER_BANK_LOCAL_3("20"),
    NCR_TERMINAL("22"),
    DIEBOLD_TERMINAL("30"),
    OTHER_BANK_LOCAL_4("31"),
    SCB_DEPRECATED("37"),
    ORFT_COUNTER_TERMINAL("50"),
    DEPOSIT_THAI_POST("52"),
    CARDLESS_QR_CODE("55"),
    ORFT_EASY_NET("60"),
    CDM_VTM("70"),
    ORFT_PROMPTPAY("80");

    private String value;

    TerminalType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }


}
