package com.scb.atmi.enums;

/**
 * To Account Type Enum
 *
 * Defines valid to account types for withdrawal transactions.
 *
 * <AUTHOR> ATM Migration Team
 * @since 2025-07-23
 */
public enum ToAccountType {
    NORMAL_WITHDRAWAL("00"),
    FAST_CASH("01");

    private String value;

    ToAccountType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }


}
