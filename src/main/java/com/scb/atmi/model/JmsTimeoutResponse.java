package com.scb.atmi.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class JmsTimeoutResponse {
    private List<ErrorDetail> errors;
    private String code;
    
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ErrorDetail {
        private String code;
        private String message;
    }
    
    public String getFirstErrorCode() {
        if (errors != null && !errors.isEmpty()) {
            return errors.get(0).getCode();
        }
        return code;
    }
}