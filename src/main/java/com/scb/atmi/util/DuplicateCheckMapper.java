package com.scb.atmi.util;

import com.scb.atmi.constant.CommonConstants;
import com.scb.atmi.library.dto.DuplicateCheckDto;
import com.scb.atmi.model.request.WithdrawalReversalRequest;
import com.scb.atmi.model.request.WithdrawalTransactionRequest;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

/**
 * Duplicate Check Mapper Utility
 * 
 * Provides utility methods for converting various transaction request types
 * to the generic DuplicateCheckDto for duplicate checking purposes.
 * 
 * This utility class centralizes the mapping logic and ensures consistent
 * conversion across different parts of the application.
 * 
 * <AUTHOR> ATM Migration Team
 * @since 2025-07-23
 */
@UtilityClass
@Slf4j
public class DuplicateCheckMapper {

    /**
     * Convert WithdrawalTransactionRequest to DuplicateCheckDto
     * 
     * Extracts the essential fields required for duplicate checking from
     * a withdrawal transaction request.
     * 
     * @param request Withdrawal transaction request
     * @return DuplicateCheckDto with essential duplicate check fields
     * @throws IllegalArgumentException if required fields are missing
     */
    public static DuplicateCheckDto fromWithdrawalRequest(WithdrawalTransactionRequest request, boolean hasOriginalMessageType) {
        if (request == null) {
            throw new IllegalArgumentException("Withdrawal request cannot be null");
        }

        try {
            return DuplicateCheckDto.builder()
                    .terminalId(request.getTerminalId())
                    .terminalSequence(request.getTerminalSequence())
                    .cardReference(request.getCardReference())
                    .messageType(request.getMessageType())
                    .originalMessageType(hasOriginalMessageType ? request.getOriginalMessageType() : null)
                    .transactionDate(request.getTransactionDate())
                    .transactionType(extractTransactionType(request))
                    .build();
        } catch (Exception e) {
            log.error("Error converting withdrawal request to duplicate check DTO: {}", e.getMessage(), e);
            throw new IllegalArgumentException("Failed to convert withdrawal request to duplicate check DTO", e);
        }
    }

    public static DuplicateCheckDto fromWithdrawalReversalRequest(WithdrawalReversalRequest request, boolean hasOriginalMessageType) {
        if (request == null) {
            throw new IllegalArgumentException("Withdrawal request cannot be null");
        }

        try {
            return DuplicateCheckDto.builder()
                    .terminalId(request.getTerminalId())
                    .terminalSequence(request.getTerminalSequence())
                    .cardReference(request.getCardReference())
                    .messageType(request.getMessageType())
                    .originalMessageType(hasOriginalMessageType ? request.getOriginalMessageType() : null)
                    .transactionDate(request.getTransactionDate())
                    .transactionType(extractReversalTransactionType(request))
                    .statusFilter1(CommonConstants.TRANSACTION_STATUS_WITHDRAWAL_SUCCESS)
                    .statusFilter2(CommonConstants.TRANSACTION_STATUS_WITHDRAWAL_REVERSE_SUCCESS)
                    .build();
        } catch (Exception e) {
            log.error("Error converting withdrawal request to duplicate check DTO: {}", e.getMessage(), e);
            throw new IllegalArgumentException("Failed to convert withdrawal request to duplicate check DTO", e);
        }
    }

    /**
     * Extract transaction type from withdrawal request
     * 
     * @param request Withdrawal transaction request
     * @return Transaction type or null if not available
     */
    private static String extractTransactionType(WithdrawalTransactionRequest request) {
        if (request.getProcessingCode() != null) {
            return request.getProcessingCode().getTransactionType();
        }
        
        // Fallback: assume withdrawal transaction type
        log.warn("Processing code not available, defaulting to withdrawal transaction type '10'");
        return "10";
    }

    private static String extractReversalTransactionType(WithdrawalReversalRequest request) {
        if (request.getProcessingCode() != null) {
            return request.getProcessingCode().getTransactionType();
        }

        // Fallback: assume withdrawal transaction type
        log.warn("Processing code not available, defaulting to withdrawal transaction type '10'");
        return "10";
    }

    /**
     * Create DuplicateCheckDto for manual construction
     * 
     * Utility method for creating DuplicateCheckDto when individual fields
     * are available but not part of a structured request object.
     * 
     * @param terminalId ATM terminal identifier
     * @param terminalSequence Terminal sequence number
     * @param cardReference Card reference number
     * @param messageType Transaction message type
     * @param transactionDate Transaction date in MMDD format
     * @param transactionType Transaction type
     * @return DuplicateCheckDto with provided fields
     */
    public static DuplicateCheckDto create(
            String terminalId,
            String terminalSequence,
            String cardReference,
            String messageType,
            String transactionDate,
            String transactionType) {
        
        return DuplicateCheckDto.builder()
                .terminalId(terminalId)
                .terminalSequence(terminalSequence)
                .cardReference(cardReference)
                .messageType(messageType)
                .transactionDate(transactionDate)
                .transactionType(transactionType)
                .build();
    }

    /**
     * Validate DuplicateCheckDto has all required fields
     * 
     * @param dto DuplicateCheckDto to validate
     * @return true if all required fields are present
     */
    public static boolean isValid(DuplicateCheckDto dto) {
        if (dto == null) {
            return false;
        }

        return dto.getTerminalId() != null && !dto.getTerminalId().trim().isEmpty() &&
               dto.getTerminalSequence() != null && !dto.getTerminalSequence().trim().isEmpty() &&
               dto.getCardReference() != null && !dto.getCardReference().trim().isEmpty() &&
               dto.getMessageType() != null && !dto.getMessageType().trim().isEmpty() &&
               dto.getTransactionDate() != null && !dto.getTransactionDate().trim().isEmpty() &&
               dto.getTransactionType() != null && !dto.getTransactionType().trim().isEmpty();
    }

    /**
     * Get validation error message for invalid DTO
     * 
     * @param dto DuplicateCheckDto to check
     * @return Error message describing missing fields, or null if valid
     */
    public static String getValidationError(DuplicateCheckDto dto) {
        if (dto == null) {
            return "DuplicateCheckDto cannot be null";
        }

        StringBuilder errors = new StringBuilder();
        
        if (dto.getTerminalId() == null || dto.getTerminalId().trim().isEmpty()) {
            errors.append("terminalId is required; ");
        }
        if (dto.getTerminalSequence() == null || dto.getTerminalSequence().trim().isEmpty()) {
            errors.append("terminalSequence is required; ");
        }
        if (dto.getCardReference() == null || dto.getCardReference().trim().isEmpty()) {
            errors.append("cardReference is required; ");
        }
        if (dto.getMessageType() == null || dto.getMessageType().trim().isEmpty()) {
            errors.append("messageType is required; ");
        }
        if (dto.getTransactionDate() == null || dto.getTransactionDate().trim().isEmpty()) {
            errors.append("transactionDate is required; ");
        }
        if (dto.getTransactionType() == null || dto.getTransactionType().trim().isEmpty()) {
            errors.append("transactionType is required; ");
        }

        return errors.length() > 0 ? errors.toString().trim() : null;
    }
}
