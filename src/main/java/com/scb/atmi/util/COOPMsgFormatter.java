package com.scb.atmi.util;

import java.math.BigDecimal;

// Utility class for formatting
public class COOPMsgFormatter {
    // Pad right with spaces
    public static String padRight(String s, int n) {
        if (s == null) s = "";
        return String.format("%-" + n + "s", s);
    }
    // Pad left with zeros
    public static String padLeftZero(String s, int n) {
        if (s == null) s = "";
        return String.format("%" + n + "s", s).replace(' ', '0');
    }
    // Pad left with zeros for numbers, without decimal
    public static String formatAmount(BigDecimal amount, int intLength, int fractionLength) {
        if (amount == null) amount = BigDecimal.ZERO;
        String number = amount.movePointRight(fractionLength)
                .setScale(0, BigDecimal.ROUND_HALF_UP)
                .toPlainString();
        return padLeftZero(number, intLength + fractionLength);
    }
    // Helper for substring YYMMDD
    public static String toYYMMDD(String mmddyy) {
        if (mmddyy == null || mmddyy.length() != 6) return "000000";
        // assumes MMDDYY format, returns YYMMDD
        return mmddyy.substring(4,6) + mmddyy.substring(0,2) + mmddyy.substring(2,4);
    }
}