package com.scb.atmi.exception;

import lombok.Getter;

/**
 * Business Exception - Base exception for business logic errors
 *
 * This exception is used for business logic errors that occur during processing.
 * It provides error codes and messages for proper error handling and logging.
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Getter
public class BusinessException extends RuntimeException {

    /**
     * -- GETTER --
     *  Gets the error code.
     *
     */
    private final String errorCode;

    /**
     * Constructs a new business exception with the specified error message.
     *
     * @param message the detail message
     */
    public BusinessException(String message) {
        super(message);
        this.errorCode = "1899";
    }

    /**
     * Constructs a new business exception with the specified error code and message.
     *
     * @param errorCode the error code
     * @param message the detail message
     */
    public BusinessException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    /**
     * Constructs a new business exception with the specified error code, message and cause.
     *
     * @param errorCode the error code
     * @param message the detail message
     * @param cause the cause
     */
    public BusinessException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    /**
     * Constructs a new business exception with the specified error code and cause.
     *
     * @param errorCode the error code
     * @param cause the cause
     */
    public BusinessException(String errorCode, Throwable cause) {
        super(cause);
        this.errorCode = errorCode;
    }

}
