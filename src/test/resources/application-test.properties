# Test Configuration Properties

# Use H2 in-memory database for testing
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.driver-class-name=org.h2.Driver

# JPA/Hibernate Properties for Testing
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Disable Kafka for testing
spring.kafka.bootstrap-servers=
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration

# Logging for tests
logging.level.scb.atm=DEBUG
logging.level.org.springframework.web=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE



# TechX properties for testing
techx.microservice.active-profile=test
techx.microservice.company-name=SCB TechX
techx.microservice.project-name=ATM Migration
techx.microservice.application-name=scb-atm
techx.microservice.log-level=DEBUG
techx.microservice.method-execution-log-enabled=false
techx.microservice.performance-checkpoints-enabled=false
techx.microservice.default-zone-id=Asia/Bangkok
techx.microservice.jackson-custom-zoned-date-time-formatter=yyyy-MM-dd'T'HH:mm:ss.SSSXXX

# Actuator for testing
management.endpoints.web.exposure.include=health,info,metrics
