pluginManagement {
    repositories {
        gradlePluginPortal()
        maven {
            url 'http://artfactop.se.scb.co.th:8081/repository/plugins-gradle-m2/'
            allowInsecureProtocol true
        }
        maven {
            url 'http://artfactop.se.scb.co.th:8081/repository/gradle-m2-proxy/'
            allowInsecureProtocol true
        }
        maven {
            url 'http://artfactop.se.scb.co.th:8081/repository/maven-releases/'
            allowInsecureProtocol true
        }
        maven {
            url 'http://artfactop.se.scb.co.th:8081/repository/maven-jcenter/'
            allowInsecureProtocol true
        }
        maven {
            url 'http://artfactop.se.scb.co.th:8081/repository/maven-central/'
            allowInsecureProtocol true
        }
        maven {
            url 'http://artfactop.se.scb.co.th:8081/repository/gradle-public/'
            allowInsecureProtocol true
        }
        maven {
            url 'http://artfactop.se.scb.co.th:8081/repository/mvnrepository/'
            allowInsecureProtocol true
        }
    }
}

rootProject.name = 'withdrawal'
