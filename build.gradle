plugins {
    id 'java'
    id 'org.springframework.boot' version '3.4.3'
    id 'io.spring.dependency-management' version '1.1.4'
    id 'jacoco'
}

group = 'com.scb.withdrawal'
version = '1.0.0'

java {
    sourceCompatibility = '21'
    targetCompatibility = '21'
}

repositories {
    mavenLocal()
    mavenCentral()
    maven {
        url 'http://artfactop.se.scb.co.th:8081/repository/plugins-gradle-m2/'
        allowInsecureProtocol true
    }
    maven {
        url 'http://artfactop.se.scb.co.th:8081/repository/gradle-m2-proxy/'
        allowInsecureProtocol true
    }
    maven {
        url 'http://artfactop.se.scb.co.th:8081/repository/maven-releases/'
        allowInsecureProtocol true
    }
    maven {
        url 'http://artfactop.se.scb.co.th:8081/repository/maven-jcenter/'
        allowInsecureProtocol true
    }
    maven {
        url 'http://artfactop.se.scb.co.th:8081/repository/maven-center/'
        allowInsecureProtocol true
    }
    maven {
        url 'http://artfactop.se.scb.co.th:8081/repository/gradle-public/'
        allowInsecureProtocol true
    }
    maven {
        url 'http://artfactop.se.scb.co.th:8081/repository/mvnrepository/'
        allowInsecureProtocol true
    }
}

dependencies {
    // ATMI Library
    implementation 'com.scb.atmi:atmi-library:1.0.0'

    // Spring Boot Starters
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'

    // Azure key vault
    implementation 'com.azure.spring:spring-cloud-azure-starter-keyvault-secrets:5.23.0'

    // Database
    runtimeOnly 'com.microsoft.sqlserver:mssql-jdbc:12.8.1.jre11'
    testImplementation 'com.h2database:h2:2.3.232'
    implementation 'org.flywaydb:flyway-core:11.10.3'
    implementation 'org.flywaydb:flyway-sqlserver:11.10.3'

    // Azure
    implementation 'com.microsoft.azure:msal4j:1.22.0'
    implementation 'com.azure:azure-storage-blob:12.31.0'
    implementation 'com.azure:azure-security-keyvault-secrets:4.9.4'

    // kafka
    implementation 'org.apache.kafka:kafka-clients:4.0.0'

    // Azure Identity authentication
    implementation 'com.azure:azure-identity:1.17.0'

    // API Documentation
    implementation "org.springdoc:springdoc-openapi-starter-webmvc-ui:2.7.0"

    // Resilience and Circuit Breaker
    implementation "io.github.resilience4j:resilience4j-spring-boot3:2.2.0"
    implementation "io.github.resilience4j:resilience4j-circuitbreaker:2.2.0"

    // Validation
    implementation 'jakarta.validation:jakarta.validation-api:3.1.0'

    // Jackson Dependencies
    implementation 'com.fasterxml.jackson.module:jackson-module-afterburner:2.19.2'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.19.2'

    // Logging Dependencies
    implementation 'net.logstash.logback:logstash-logback-encoder:8.0'
    
    // Utilities
    implementation "org.mapstruct:mapstruct:1.5.5.Final"
    annotationProcessor "org.mapstruct:mapstruct-processor:1.5.5.Final"
    annotationProcessor "org.projectlombok:lombok-mapstruct-binding:0.2.0"
    compileOnly 'org.projectlombok:lombok:1.18.36'
    annotationProcessor 'org.projectlombok:lombok:1.18.36'

    // Testing
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
}

test {
    useJUnitPlatform()
    maxHeapSize = '1G'
    jacoco {
        finalizedBy jacocoTestReport
        destinationFile = file("build/jacoco/sonar.exec")
    }
    jacocoTestReport {
        dependsOn test
    }
    testLogging {
        events 'PASSED', 'FAILED', 'SKIPPED'
    }
}

jacocoTestReport {
    executionData fileTree(project.layout.buildDirectory).include("/jacoco/*.exec")
    reports {
        xml.required.set(true)
        csv.required.set(true)
        html.required.set(true)
        html.destination file(layout.buildDirectory.dir('jacocoHtml'))
    }
    afterEvaluate {
        classDirectories.setFrom(files(classDirectories.files.collect {
            fileTree(dir: it, exclude: [
                    "**/com/scb/bizo/Application.class",
                    "**/com/scb/bizo/model*",
                    "**/com/scb/bizo/repository*",
                    "**/com/scb/bizo/exception*"
            ])
        }))
    }
}

jacocoTestCoverageVerification {
    violationRules {
        rule {
            limit {
                minimum = 0.10
            }
        }
    }
}

check.dependsOn jacocoTestCoverageVerification