ARG ARTIFACT_ID="withdrawal"
ARG VERSION="1.0.0" # can be overrided via `build-arg`

FROM harbordev.se.scb.co.th/library/cc/common:base AS common

# -- Build stage --
FROM harbordev.se.scb.co.th/library/gradle:8.10.0-jdk21-alpine AS builder
ARG ARTIFACT_ID
ARG VERSION

# Install build dependencies (optional)
# USER root
# RUN apk update && apk add --no-cache python3 make g++

# Build source code
COPY . .
RUN chmod +x gradlew
RUN ./gradlew clean build -x test

# -- Final stage --
FROM harbordev.se.scb.co.th/library/eclipse-temurin:21-jre-alpine
ARG ARTIFACT_ID
ARG VERSION

# Fix SAT scan
RUN apk update && apk upgrade --no-cache && apk add tzdata --no-cache

# Install certs (to linux and java)
COPY --from=common /certs/* /usr/local/share/ca-certificates
RUN update-ca-certificates
COPY --from=common /scripts /scripts
RUN /bin/sh /scripts/java_add_multi_cert.sh

# Switch to nonroot user
RUN adduser -D -H -s /bin/sh -u 1000 nonroot
USER nonroot
WORKDIR /app

# Copy app from build stage
COPY --from=builder --chown=nonroot:nonroot "/home/<USER>/build/libs/${ARTIFACT_ID}-${VERSION}.jar" app.jar

# Start app
ENTRYPOINT ["java", "-jar", "app.jar"]

# Default env
ENV TZ=Asia/Bangkok