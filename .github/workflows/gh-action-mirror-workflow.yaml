name: Mirror GitHub → GitLab
# .github/workflows/mirror-to-gitlab.yml

on:
  push:
    branches: ["**"]
    tags: ["**"]
  workflow_dispatch:

concurrency:
  group: mirror-${{ github.repository }}
  cancel-in-progress: false

jobs:
  mirror:
    runs-on: ubuntu-latest
    env:
      GH_URL: ${{ secrets.GH_SOURCE_URL }}   # e.g. https://github.com/org/repo.git
      GL_URL: ${{ secrets.GL_TARGET_URL }}   # e.g. https://gitlab.com/group/repo.git
      GH_PAT: ${{ secrets.GH_PAT }}          # GitHub PAT (repo scope)
      GL_PAT: ${{ secrets.GL_PAT }}          # GitLab PAT (write_repository or api)
      DRY_RUN: ${{ vars.DRY_RUN || '0' }}    # set to '1' in repo/org Variables to preview only

    steps:
      - name: Prep git (and optional LFS)
        run: |
          set -euo pipefail
          sudo apt-get update -y
          sudo apt-get install -y git git-lfs
          git lfs install

      - name: Compute deletions on GitLab (branches & tags not in GitHub)
        id: compute
        run: |
          set -euo pipefail

          AUTH_GH="${GH_URL/https:\/\//https://$GH_PAT@}"
          AUTH_GL="${GL_URL/https:\/\//https://oauth2:$GL_PAT@}"

          work="$(mktemp -d)"
          echo "workdir=$work" >> "$GITHUB_OUTPUT"

          # List HEADS
          git ls-remote --heads "$AUTH_GH" | awk '{print $2}' | sed 's#^refs/heads/##' | sort -u > "$work/gh_heads.txt"
          git ls-remote --heads "$AUTH_GL" | awk '{print $2}' | sed 's#^refs/heads/##' | sort -u > "$work/gl_heads.txt"

          # List TAGS (strip annotated ^{} lines)
          git ls-remote --tags --refs "$AUTH_GH" | awk '{print $2}' | sed 's#^refs/tags/##' | sort -u > "$work/gh_tags.txt"
          git ls-remote --tags --refs "$AUTH_GL" | awk '{print $2}' | sed 's#^refs/tags/##' | sort -u > "$work/gl_tags.txt"

          # What exists on GitLab but NOT on GitHub → delete on GitLab
          comm -23 "$work/gl_heads.txt" "$work/gh_heads.txt" > "$work/delete_heads.txt" || true
          comm -23 "$work/gl_tags.txt"  "$work/gh_tags.txt"  > "$work/delete_tags.txt"  || true

          echo "[INFO] Branches to delete:"
          cat "$work/delete_heads.txt" || true
          echo "[INFO] Tags to delete:"
          cat "$work/delete_tags.txt"  || true

          # Expose counts
          echo "del_heads=$(wc -l < "$work/delete_heads.txt" | tr -d ' ')" >> "$GITHUB_OUTPUT"
          echo "del_tags=$(wc -l < "$work/delete_tags.txt" | tr -d ' ')"  >> "$GITHUB_OUTPUT"

      - name: Delete missing branches on GitLab
        if: steps.compute.outputs.del_heads != '0' && env.DRY_RUN != '1'
        run: |
          set -euo pipefail
          AUTH_GL="${GL_URL/https:\/\//https://oauth2:$GL_PAT@}"
          while IFS= read -r b || [[ -n "$b" ]]; do
            [[ -z "$b" ]] && continue
            echo "Deleting branch: $b"
            git push "$AUTH_GL" ":refs/heads/$b" || echo "[WARN] Failed to delete branch '$b' (protected?)"
          done < "${{ steps.compute.outputs.workdir }}/delete_heads.txt"

      - name: Delete missing tags on GitLab
        if: steps.compute.outputs.del_tags != '0' && env.DRY_RUN != '1'
        run: |
          set -euo pipefail
          AUTH_GL="${GL_URL/https:\/\//https://oauth2:$GL_PAT@}"
          while IFS= read -r t || [[ -n "$t" ]]; do
            [[ -z "$t" ]] && continue
            echo "Deleting tag: $t"
            git push "$AUTH_GL" ":refs/tags/$t" || echo "[WARN] Failed to delete tag '$t' (protected?)"
          done < "${{ steps.compute.outputs.workdir }}/delete_tags.txt"

      - name: Sync branches & tags (avoid hidden refs)
        if: env.DRY_RUN != '1'
        run: |
          set -euo pipefail
          AUTH_GH="${GH_URL/https:\/\//https://$GH_PAT@}"
          AUTH_GL="${GL_URL/https:\/\//https://oauth2:$GL_PAT@}"

          git clone --bare "$AUTH_GH" repo.git
          cd repo.git
          git fetch --all --prune

          # Scrub refs that can trigger "deny updating a hidden ref"
          git update-ref -d refs/remotes/origin/HEAD || true
          git for-each-ref --format='%(refname)' 'refs/pull/*'        | xargs -r -n1 git update-ref -d
          git for-each-ref --format='%(refname)' 'refs/changes/*'     | xargs -r -n1 git update-ref -d
          git for-each-ref --format='%(refname)' 'refs/keep-around/*' | xargs -r -n1 git update-ref -d

          echo "[INFO] Pushing branches (force + prune)…"
          git push --prune "$AUTH_GL" +refs/heads/*:refs/heads/*

          echo "[INFO] Pushing tags (force + prune)…"
          git push --prune "$AUTH_GL" +refs/tags/*:refs/tags/*

      - name: Push LFS objects (optional)
        if: env.DRY_RUN != '1'
        run: |
          set -euo pipefail
          cd repo.git || exit 0
          AUTH_GL="${GL_URL/https:\/\//https://oauth2:$GL_PAT@}"
          git lfs fetch --all || true
          git lfs push --all "$AUTH_GL" || true