# Withdraw Backend Service

Welcome to the withdraw backend service. This application is developed using Spring Boot with Java 21.

## Table of Contents

- [Installation](#installation)

## Installation

To set up the withdraw backend service, follow these steps:

### Step 1: Install Java 21

1. Open a terminal window.
2. Use Homebrew to install Java 21 by executing:
   ```bash
   brew install openjdk@21
   ```

3. After the installation is complete, you'll need to symlink to `/Library/Java/JavaVirtualMachines/openjdk-21.jdk` in order to make your terminal finds the `java` command.
   ```bash 
   # for mac with Apple silicon
   sudo ln -sfn /opt/homebrew/opt/openjdk@21/libexec/openjdk.jdk /Library/Java/JavaVirtualMachines/openjdk-21.jdk
   # for mac with Intel
   sudo ln -sfn /usr/local/Cellar/openjdk/openjdk@21/libexec/openjdk.jdk /Library/Java/JavaVirtualMachines/openjdk-21.jdk
   ```

4. To allow JVM to find the correct version, also include:
   ```bash
   export JAVA_HOME=$(/usr/libexec/java_home -v 21)
   ```

5. Restart your terminal or run `source ~/.zshrc` or the equivalent for your shell to apply changes.

6. Verify the installation by checking the Java version:
   ```bash
   java -version
   ```

   You should see output indicating Java 21 is the active version.

### Step 2: Clone ATMI Library

1. Clone ATMI library to your local machine using the following command:
   ```bash
   git clone https://gitdop.se.scb.co.th/ap2442-atmi/services/backend/atmi-library.git
   ```

2. Navigate into the cloned library:
   ```bash
   cd atmi-library
   ```

3. On terminal, run this command to install the library on your machine:
   ```bash
   ./gradlew build && ./gradlew publishToMavenLocal
   ```
